package com.logictrue.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 设备检测数据表格数据实体类
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("device_detection_table_data")
public class DeviceDetectionTableData {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 检测数据主表ID */
    @TableField("detection_data_id")
    private Long detectionDataId;

    /** Sheet ID */
    @TableField("sheet_id")
    private String sheetId;

    /** Sheet名称 */
    @TableField("sheet_name")
    private String sheetName;

    /** Sheet索引 */
    @TableField("sheet_index")
    private Integer sheetIndex;

    /** 数据行索引 */
    @TableField("row_index")
    private Integer rowIndex;

    /** 行数据JSON格式 */
    @TableField("row_data")
    private String rowData;

    /** 行顺序 */
    @TableField("row_order")
    private Integer rowOrder;

    /** 创建时间 */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}

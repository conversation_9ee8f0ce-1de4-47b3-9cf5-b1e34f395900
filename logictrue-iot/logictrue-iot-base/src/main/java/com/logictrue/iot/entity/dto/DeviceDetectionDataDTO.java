package com.logictrue.iot.entity.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;

/**
 * 设备检测数据DTO
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
public class DeviceDetectionDataDTO {

    /** 主键ID */
    private Long id;

    /** 设备编码 */
    @NotBlank(message = "设备编码不能为空")
    private String deviceCode;

    /** 模板ID */
    private Long templateId;

    /** 模板名称 */
    private String templateName;

    /** 原始文件名 */
    private String fileName;

    /** 解析状态：0-待解析，1-解析成功，2-解析失败 */
    private Integer parseStatus;

    /** 备注 */
    private String remark;

    /** 上传文件 */
    private MultipartFile file;

    // 查询条件
    /** 设备名称（查询条件） */
    private String deviceName;

    /** 开始时间（查询条件） */
    private String startTime;

    /** 结束时间（查询条件） */
    private String endTime;
}

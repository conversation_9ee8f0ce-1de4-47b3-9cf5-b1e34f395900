package com.logictrue.iot.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.logictrue.common.core.domain.R;
import com.logictrue.common.core.web.controller.BaseController;
import com.logictrue.iot.entity.ExcelTemplate;
import com.logictrue.iot.entity.dto.ExcelTemplateDTO;
import com.logictrue.iot.service.IExcelTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * Excel模板设计控制器
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Api(tags = "Excel模板设计")
@Slf4j
@RestController
@RequestMapping("/excel-template")
@Validated
public class ExcelTemplateController extends BaseController {

    @Autowired
    private IExcelTemplateService excelTemplateService;

    /**
     * 保存Excel模板设计
     */
    @ApiOperation("保存Excel模板设计")
    @PostMapping("/save")
    public R<ExcelTemplate> saveTemplate(@Valid @RequestBody ExcelTemplateDTO templateDTO) {
        try {
            // 验证模板编码唯一性
            if (!excelTemplateService.isTemplateCodeUnique(templateDTO.getTemplateCode(), templateDTO.getId())) {
                return R.fail("模板编码已存在");
            }

            ExcelTemplate result = excelTemplateService.saveTemplateDesign(templateDTO);
            if (result != null) {
                return R.ok(result, "保存成功");
            } else {
                return R.fail("保存失败");
            }
        } catch (Exception e) {
            log.error("保存Excel模板失败", e);
            return R.fail("保存失败: " + e.getMessage());
        }
    }

    /**
     * 获取模板详情
     */
    @ApiOperation("获取模板详情")
    @GetMapping("/{id}")
    public R<ExcelTemplate> getTemplate(@ApiParam("模板ID") @PathVariable Long id) {
        try {
            ExcelTemplate template = excelTemplateService.getTemplateWithDetails(id);
            if (template != null) {
                return R.ok(template);
            } else {
                return R.fail("模板不存在");
            }
        } catch (Exception e) {
            log.error("获取模板详情失败", e);
            return R.fail("获取失败: " + e.getMessage());
        }
    }

    /**
     * 根据模板编码获取模板
     */
    @ApiOperation("根据模板编码获取模板")
    @GetMapping("/code/{templateCode}")
    public R<ExcelTemplate> getTemplateByCode(@ApiParam("模板编码") @PathVariable String templateCode) {
        try {
            ExcelTemplate template = excelTemplateService.getByTemplateCode(templateCode);
            if (template != null) {
                return R.ok(template);
            } else {
                return R.fail("模板不存在");
            }
        } catch (Exception e) {
            log.error("根据编码获取模板失败", e);
            return R.fail("获取失败: " + e.getMessage());
        }
    }

    /**
     * 根据设备类型获取模板列表
     */
    @ApiOperation("根据设备类型获取模板列表")
    @GetMapping("/device-type/{deviceType}")
    public R<List<ExcelTemplate>> getTemplatesByDeviceType(@ApiParam("设备类型") @PathVariable String deviceType) {
        try {
            List<ExcelTemplate> templates = excelTemplateService.getByDeviceType(deviceType);
            return R.ok(templates);
        } catch (Exception e) {
            log.error("根据设备类型获取模板列表失败", e);
            return R.fail("获取失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询模板列表
     */
    @ApiOperation("分页查询模板列表")
    @GetMapping("/pageList")
    public R<IPage<ExcelTemplate>> pageList(ExcelTemplateDTO templateDTO,
                                           @RequestParam(value = "pageSize", defaultValue = "10") Long pageSize,
                                           @RequestParam(value = "pageNum", defaultValue = "1") Long pageNum) {
        try {
            IPage<ExcelTemplate> page = getPage();
            excelTemplateService.pageList(page, templateDTO);
            return R.ok(page);
        } catch (Exception e) {
            log.error("分页查询模板列表失败", e);
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 删除模板
     */
    @ApiOperation("删除模板")
    @DeleteMapping("/{id}")
    public R<Boolean> deleteTemplate(@ApiParam("模板ID") @PathVariable Long id) {
        try {
            boolean result = excelTemplateService.deleteTemplate(id);
            if (result) {
                return R.ok(true, "删除成功");
            } else {
                return R.fail("删除失败");
            }
        } catch (Exception e) {
            log.error("删除模板失败", e);
            return R.fail("删除失败: " + e.getMessage());
        }
    }

    /**
     * 复制模板
     */
    @ApiOperation("复制模板")
    @PostMapping("/copy/{id}")
    public R<Boolean> copyTemplate(
            @ApiParam("源模板ID") @PathVariable Long id,
            @ApiParam("新模板名称") @RequestParam String newTemplateName,
            @ApiParam("新模板编码") @RequestParam String newTemplateCode) {
        try {
            // 验证新模板编码唯一性
            if (!excelTemplateService.isTemplateCodeUnique(newTemplateCode, null)) {
                return R.fail("新模板编码已存在");
            }

            boolean result = excelTemplateService.copyTemplate(id, newTemplateName, newTemplateCode);
            if (result) {
                return R.ok(true, "复制成功");
            } else {
                return R.fail("复制失败");
            }
        } catch (Exception e) {
            log.error("复制模板失败", e);
            return R.fail("复制失败: " + e.getMessage());
        }
    }

    /**
     * 导出Excel模板（带数据）
     */
    @ApiOperation("导出Excel模板（带数据）")
    @PostMapping("/export/{id}")
    public void exportTemplate(
            @ApiParam("模板ID") @PathVariable Long id,
            @ApiParam("填充数据") @RequestBody(required = false) List<Map<String, Object>> data,
            HttpServletResponse response) {
        try {
            excelTemplateService.exportExcelTemplate(id, data, response);
        } catch (Exception e) {
            log.error("导出Excel模板失败", e);
            try {
                response.setContentType("application/json;charset=utf-8");
                response.getWriter().write("{\"code\":500,\"msg\":\"导出失败: " + e.getMessage() + "\"}");
            } catch (Exception ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    /**
     * 导出空白Excel模板
     */
    @ApiOperation("导出空白Excel模板")
    @GetMapping("/export-blank/{id}")
    public void exportBlankTemplate(
            @ApiParam("模板ID") @PathVariable Long id,
            HttpServletResponse response) {
        try {
            excelTemplateService.exportBlankTemplate(id, response);
        } catch (Exception e) {
            log.error("导出空白Excel模板失败", e);
            try {
                response.setContentType("application/json;charset=utf-8");
                response.getWriter().write("{\"code\":500,\"msg\":\"导出失败: " + e.getMessage() + "\"}");
            } catch (Exception ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    /**
     * 验证模板编码唯一性
     */
    @ApiOperation("验证模板编码唯一性")
    @GetMapping("/check-code")
    public R<Boolean> checkTemplateCode(
            @ApiParam("模板编码") @RequestParam String templateCode,
            @ApiParam("排除的ID") @RequestParam(required = false) Long excludeId) {
        try {
            boolean isUnique = excelTemplateService.isTemplateCodeUnique(templateCode, excludeId);
            return R.ok(isUnique);
        } catch (Exception e) {
            log.error("验证模板编码唯一性失败", e);
            return R.fail("验证失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用模板
     */
    @ApiOperation("启用/禁用模板")
    @PutMapping("/status/{id}")
    public R<Boolean> updateStatus(
            @ApiParam("模板ID") @PathVariable Long id,
            @ApiParam("状态") @RequestParam Integer status) {
        try {
            ExcelTemplate template = new ExcelTemplate();
            template.setId(id);
            template.setStatus(status);
            boolean result = excelTemplateService.updateById(template);
            if (result) {
                return R.ok(true, "状态更新成功");
            } else {
                return R.fail("状态更新失败");
            }
        } catch (Exception e) {
            log.error("更新模板状态失败", e);
            return R.fail("更新失败: " + e.getMessage());
        }
    }
}

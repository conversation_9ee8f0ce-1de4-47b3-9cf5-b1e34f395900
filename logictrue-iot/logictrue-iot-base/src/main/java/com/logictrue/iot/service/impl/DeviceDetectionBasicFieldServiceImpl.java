package com.logictrue.iot.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.logictrue.iot.entity.DeviceDetectionBasicField;
import com.logictrue.iot.mapper.DeviceDetectionBasicFieldMapper;
import com.logictrue.iot.service.IDeviceDetectionBasicFieldService;
import org.springframework.stereotype.Service;

/**
 * 设备检测数据基础字段Service实现类
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
public class DeviceDetectionBasicFieldServiceImpl extends ServiceImpl<DeviceDetectionBasicFieldMapper, DeviceDetectionBasicField> 
        implements IDeviceDetectionBasicFieldService {

}

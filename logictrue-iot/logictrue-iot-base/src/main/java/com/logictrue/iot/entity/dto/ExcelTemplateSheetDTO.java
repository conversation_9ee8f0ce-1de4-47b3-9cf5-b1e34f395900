package com.logictrue.iot.entity.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Excel模板Sheet数据传输对象
 *
 */
@Data
public class ExcelTemplateSheetDTO {

    /** 主键ID */
    private Long id;

    /** 模板ID */
    private Long templateId;

    /** Sheet ID */
    @NotBlank(message = "Sheet ID不能为空")
    private String sheetId;

    /** Sheet名称 */
    @NotBlank(message = "Sheet名称不能为空")
    private String sheetName;

    /** Sheet索引（从0开始） */
    private Integer sheetIndex;

    /** 最大列数 */
    @NotNull(message = "最大列数不能为空")
    private Integer maxColumns;

    /** 最大行数 */
    @NotNull(message = "最大行数不能为空")
    private Integer maxRows;

    /** 列宽度配置 */
    private List<Integer> columnWidths;

    /** 排序序号 */
    private Integer sortOrder;

    /** 单元格配置列表 */
    private List<ExcelTemplateDTO.ExcelTemplateCellDTO> cells;

    /** 字段配置列表 */
    private List<ExcelTemplateDTO.ExcelTemplateFieldDTO> fields;
}

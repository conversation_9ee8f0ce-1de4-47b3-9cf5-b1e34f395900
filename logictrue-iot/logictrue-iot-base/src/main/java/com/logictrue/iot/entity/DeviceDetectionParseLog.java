package com.logictrue.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 设备检测数据解析日志实体类
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("device_detection_parse_log")
public class DeviceDetectionParseLog {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 检测数据主表ID */
    @TableField("detection_data_id")
    private Long detectionDataId;

    /** 日志级别：INFO-信息，WARN-警告，ERROR-错误 */
    @TableField("log_level")
    private String logLevel;

    /** 日志消息 */
    @TableField("log_message")
    private String logMessage;

    /** Sheet ID */
    @TableField("sheet_id")
    private String sheetId;

    /** Sheet名称 */
    @TableField("sheet_name")
    private String sheetName;

    /** 位置信息 */
    @TableField("position")
    private String position;

    /** 字段编码 */
    @TableField("field_code")
    private String fieldCode;

    /** 创建时间 */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}

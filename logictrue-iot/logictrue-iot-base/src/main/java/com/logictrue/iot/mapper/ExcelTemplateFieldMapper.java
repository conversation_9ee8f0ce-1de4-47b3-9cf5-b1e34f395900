package com.logictrue.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logictrue.iot.entity.ExcelTemplateField;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Excel模板字段配置Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Mapper
public interface ExcelTemplateFieldMapper extends BaseMapper<ExcelTemplateField> {

    /**
     * 根据模板ID查询字段配置
     *
     * @param templateId 模板ID
     * @return 字段配置列表
     */
    @Select("SELECT * FROM excel_template_field WHERE template_id = #{templateId} ORDER BY sort_order, id")
    List<ExcelTemplateField> selectByTemplateId(@Param("templateId") Long templateId);

    /**
     * 根据模板ID删除字段配置
     *
     * @param templateId 模板ID
     * @return 删除数量
     */
    @Delete("DELETE FROM excel_template_field WHERE template_id = #{templateId}")
    int deleteByTemplateId(@Param("templateId") Long templateId);

    /**
     * 根据字段分类查询字段
     *
     * @param templateId 模板ID
     * @param fieldCategory 字段分类
     * @return 字段配置列表
     */
    @Select("SELECT * FROM excel_template_field WHERE template_id = #{templateId} AND field_category = #{fieldCategory} ORDER BY sort_order")
    List<ExcelTemplateField> selectByCategory(@Param("templateId") Long templateId,
                                            @Param("fieldCategory") String fieldCategory);

    /**
     * 批量插入字段配置
     *
     * @param fields 字段配置列表
     * @return 插入数量
     */
    int batchInsert(@Param("fields") List<ExcelTemplateField> fields);
}

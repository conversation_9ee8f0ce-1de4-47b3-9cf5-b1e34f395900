package com.logictrue.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logictrue.iot.entity.DeviceDetectionData;
import com.logictrue.iot.entity.vo.DeviceDetectionDataVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 设备检测数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Mapper
public interface DeviceDetectionDataMapper extends BaseMapper<DeviceDetectionData> {

    /**
     * 分页查询设备检测数据列表（关联设备信息）
     */
    IPage<DeviceDetectionDataVO> selectDetectionDataPage(Page<DeviceDetectionDataVO> page, 
                                                        @Param("deviceCode") String deviceCode,
                                                        @Param("deviceName") String deviceName,
                                                        @Param("templateName") String templateName,
                                                        @Param("parseStatus") Integer parseStatus,
                                                        @Param("startTime") String startTime,
                                                        @Param("endTime") String endTime);

    /**
     * 根据ID查询检测数据详情（包含关联数据）
     */
    DeviceDetectionDataVO selectDetectionDataDetail(@Param("id") Long id);
}

package com.logictrue.iot.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备数据导入结果DTO
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel(description = "设备数据导入结果")
public class DeviceImportResult {

    @ApiModelProperty(value = "任务ID")
    private String taskId;

    @ApiModelProperty(value = "导入状态：0-进行中，1-成功，2-失败，3-部分成功")
    private Integer status;

    @ApiModelProperty(value = "导入进度百分比（0-100）")
    private Integer progress;

    @ApiModelProperty(value = "当前处理步骤")
    private String currentStep;

    @ApiModelProperty(value = "导入的设备数量")
    private Integer importedDeviceCount;

    @ApiModelProperty(value = "跳过的设备数量")
    private Integer skippedDeviceCount;

    @ApiModelProperty(value = "失败的设备数量")
    private Integer failedDeviceCount;

    @ApiModelProperty(value = "导入的检测数据记录数")
    private Long importedDetectionDataCount;

    @ApiModelProperty(value = "导入的原始文件数量")
    private Integer importedRawFileCount;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "完成时间")
    private LocalDateTime completeTime;

    @ApiModelProperty(value = "错误信息")
    private String errorMessage;

    @ApiModelProperty(value = "成功导入的设备编码列表")
    private List<String> successDeviceCodes;

    @ApiModelProperty(value = "失败的设备编码列表")
    private List<String> failedDeviceCodes;

    @ApiModelProperty(value = "导入详细信息列表")
    private List<ImportDetail> importDetails;

    @ApiModelProperty(value = "导入统计信息")
    private ImportStatistics statistics;

    /**
     * 导入详细信息
     */
    @Data
    @ApiModel(description = "导入详细信息")
    public static class ImportDetail {
        @ApiModelProperty(value = "设备编码")
        private String deviceCode;

        @ApiModelProperty(value = "导入状态：SUCCESS-成功，FAILED-失败，SKIPPED-跳过")
        private String status;

        @ApiModelProperty(value = "处理消息")
        private String message;

        @ApiModelProperty(value = "导入的数据类型")
        private String dataType;

        @ApiModelProperty(value = "处理时间")
        private LocalDateTime processTime;
    }

    /**
     * 导入统计信息
     */
    @Data
    @ApiModel(description = "导入统计信息")
    public static class ImportStatistics {
        @ApiModelProperty(value = "总设备数量")
        private Integer totalDeviceCount;

        @ApiModelProperty(value = "设备基础信息导入数量")
        private Integer deviceInfoImportCount;

        @ApiModelProperty(value = "设备模板绑定导入数量")
        private Integer templateBindingImportCount;

        @ApiModelProperty(value = "基础字段导入数量")
        private Long basicFieldImportCount;

        @ApiModelProperty(value = "表格数据行导入数量")
        private Long tableDataRowImportCount;

        @ApiModelProperty(value = "解析日志导入数量")
        private Long parseLogImportCount;

        @ApiModelProperty(value = "原始文件导入数量")
        private Integer rawFileImportCount;

        @ApiModelProperty(value = "成功率")
        private Double successRate;
    }

    /**
     * 检查是否导入完成
     */
    public boolean isCompleted() {
        return status != null && (status == 1 || status == 2 || status == 3);
    }

    /**
     * 检查是否导入成功
     */
    public boolean isSuccess() {
        return status != null && status == 1;
    }

    /**
     * 检查是否导入失败
     */
    public boolean isFailed() {
        return status != null && status == 2;
    }

    /**
     * 检查是否部分成功
     */
    public boolean isPartialSuccess() {
        return status != null && status == 3;
    }
}

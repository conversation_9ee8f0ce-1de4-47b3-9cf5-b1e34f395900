package com.logictrue.iot.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备数据导出结果DTO
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel(description = "设备数据导出结果")
public class DeviceExportResult {

    @ApiModelProperty(value = "任务ID")
    private String taskId;

    @ApiModelProperty(value = "导出状态：0-进行中，1-成功，2-失败")
    private Integer status;

    @ApiModelProperty(value = "导出进度百分比（0-100）")
    private Integer progress;

    @ApiModelProperty(value = "当前处理步骤")
    private String currentStep;

    @ApiModelProperty(value = "导出的设备数量")
    private Integer deviceCount;

    @ApiModelProperty(value = "导出的检测数据记录数")
    private Long detectionDataCount;

    @ApiModelProperty(value = "导出的原始文件数量")
    private Integer rawFileCount;

    @ApiModelProperty(value = "导出文件大小（字节）")
    private Long fileSize;

    @ApiModelProperty(value = "导出文件路径")
    private String filePath;

    @ApiModelProperty(value = "导出文件名")
    private String fileName;

    @ApiModelProperty(value = "下载URL")
    private String downloadUrl;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "完成时间")
    private LocalDateTime completeTime;

    @ApiModelProperty(value = "错误信息")
    private String errorMessage;

    @ApiModelProperty(value = "导出的设备编码列表")
    private List<String> exportedDeviceCodes;

    @ApiModelProperty(value = "导出统计信息")
    private ExportStatistics statistics;

    /**
     * 导出统计信息
     */
    @Data
    @ApiModel(description = "导出统计信息")
    public static class ExportStatistics {
        @ApiModelProperty(value = "设备基础信息数量")
        private Integer deviceInfoCount;

        @ApiModelProperty(value = "设备模板绑定数量")
        private Integer templateBindingCount;

        @ApiModelProperty(value = "基础字段数量")
        private Long basicFieldCount;

        @ApiModelProperty(value = "表格数据行数")
        private Long tableDataRowCount;

        @ApiModelProperty(value = "解析日志数量")
        private Long parseLogCount;

        @ApiModelProperty(value = "原始文件总大小（字节）")
        private Long rawFilesTotalSize;
    }

    /**
     * 检查是否导出完成
     */
    public boolean isCompleted() {
        return status != null && (status == 1 || status == 2);
    }

    /**
     * 检查是否导出成功
     */
    public boolean isSuccess() {
        return status != null && status == 1;
    }

    /**
     * 检查是否导出失败
     */
    public boolean isFailed() {
        return status != null && status == 2;
    }
}

package com.logictrue.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * Excel单元格配置实体类
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("excel_template_cell")
public class ExcelTemplateCell {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 模板ID */
    @TableField("template_id")
    private Long templateId;

    /** Sheet ID */
    @TableField("sheet_id")
    private String sheetId;

    /** Sheet名称 */
    @TableField("sheet_name")
    private String sheetName;

    /** Sheet索引（从0开始） */
    @TableField("sheet_index")
    private Integer sheetIndex;

    /** 行索引（从0开始） */
    @TableField("row_index")
    private Integer rowIndex;

    /** 列索引（从0开始） */
    @TableField("col_index")
    private Integer colIndex;

    /** 单元格位置（如A1, B2） */
    @TableField("cell_position")
    private String cellPosition;

    /** 单元格内容 */
    @TableField("content")
    private String content;

    /** 单元格类型：label-标签，value-值，header-表头 */
    @TableField("cell_type")
    private String cellType;

    /** 字段类型：text-文本，number-数字，date-日期，header-表头 */
    @TableField("field_type")
    private String fieldType;

    /** 字段编码 */
    @TableField("field_code")
    private String fieldCode;

    /** 排序序号 */
    @TableField("sort_order")
    private Integer sortOrder;

    /** 是否为合并单元格：1-是，0-否 */
    @TableField("merged")
    private Boolean merged;

    /** 是否为合并单元格的主单元格：1-是，0-否 */
    @TableField("merge_main")
    private Boolean mergeMain;

    /** 合并的行数 */
    @TableField("merge_row_span")
    private Integer mergeRowSpan;

    /** 合并的列数 */
    @TableField("merge_col_span")
    private Integer mergeColSpan;

    /** 是否隐藏（被合并的单元格）：1-是，0-否 */
    @TableField("hidden")
    private Boolean hidden;

    /** 创建时间 */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 更新时间 */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}

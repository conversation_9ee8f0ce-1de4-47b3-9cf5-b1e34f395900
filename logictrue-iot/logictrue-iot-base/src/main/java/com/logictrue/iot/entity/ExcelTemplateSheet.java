package com.logictrue.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Excel模板Sheet配置实体类
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("excel_template_sheet")
public class ExcelTemplateSheet {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 模板ID */
    @TableField("template_id")
    private Long templateId;

    /** Sheet ID */
    @TableField("sheet_id")
    private String sheetId;

    /** Sheet名称 */
    @TableField("sheet_name")
    private String sheetName;

    /** Sheet索引（从0开始） */
    @TableField("sheet_index")
    private Integer sheetIndex;

    /** 最大列数 */
    @TableField("max_columns")
    private Integer maxColumns;

    /** 最大行数 */
    @TableField("max_rows")
    private Integer maxRows;

    /** 列宽度配置JSON */
    @TableField("column_widths")
    private String columnWidths;

    /** 排序序号 */
    @TableField("sort_order")
    private Integer sortOrder;

    /** 创建时间 */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 更新时间 */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /** 单元格配置列表（非数据库字段） */
    @TableField(exist = false)
    private List<ExcelTemplateCell> cells;

    /** 字段配置列表（非数据库字段） */
    @TableField(exist = false)
    private List<ExcelTemplateField> fields;
}

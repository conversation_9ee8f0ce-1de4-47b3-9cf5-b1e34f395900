package com.logictrue.iot.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.logictrue.iot.entity.DeviceDetectionTableHeader;
import com.logictrue.iot.mapper.DeviceDetectionTableHeaderMapper;
import com.logictrue.iot.service.IDeviceDetectionTableHeaderService;
import org.springframework.stereotype.Service;

/**
 * 设备检测数据表格表头Service实现类
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
public class DeviceDetectionTableHeaderServiceImpl extends ServiceImpl<DeviceDetectionTableHeaderMapper, DeviceDetectionTableHeader> 
        implements IDeviceDetectionTableHeaderService {

}

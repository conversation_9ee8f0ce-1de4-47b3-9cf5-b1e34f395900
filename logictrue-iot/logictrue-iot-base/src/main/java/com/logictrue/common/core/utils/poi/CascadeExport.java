package com.logictrue.common.core.utils.poi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.usermodel.*;

import java.io.FileOutputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CascadeExport {

	@SuppressWarnings("resource")
	public static void main(String[] args) {
		JSONArray array = JSON.parseArray(
				"[{\"value\":100,\"parentId\":0,\"label\":\"新仁集团\",\"dataType\":260,\"children\":[{\"value\":220,\"parentId\":100,\"label\":\"贵州新仁新能源科技有限公司\",\"dataType\":261,\"children\":[{\"value\":199,\"parentId\":220,\"label\":\"材料厂区\",\"dataType\":267,\"children\":[{\"value\":214,\"parentId\":199,\"label\":\"五六溶解车间\",\"dataType\":262},{\"value\":221,\"parentId\":199,\"label\":\"前驱体维修A区\",\"dataType\":276,\"children\":[{\"value\":200,\"parentId\":221,\"label\":\"三元一车间\",\"dataType\":262},{\"value\":201,\"parentId\":221,\"label\":\"三元二车间\",\"dataType\":262},{\"value\":202,\"parentId\":221,\"label\":\"三元三车间\",\"dataType\":262},{\"value\":212,\"parentId\":221,\"label\":\"三元四车间\",\"dataType\":262},{\"value\":242,\"parentId\":221,\"label\":\"中试车间\",\"dataType\":262},{\"value\":243,\"parentId\":221,\"label\":\"返溶车间\",\"dataType\":262},{\"value\":244,\"parentId\":221,\"label\":\"成品仓库\",\"dataType\":262}]},{\"value\":222,\"parentId\":199,\"label\":\"前驱体维修B区\",\"dataType\":276,\"children\":[{\"value\":213,\"parentId\":222,\"label\":\"三元五车间\",\"dataType\":262},{\"value\":215,\"parentId\":222,\"label\":\"三元六车间\",\"dataType\":262},{\"value\":245,\"parentId\":222,\"label\":\"溶解车间\",\"dataType\":262},{\"value\":246,\"parentId\":222,\"label\":\"锅炉房\",\"dataType\":262},{\"value\":247,\"parentId\":222,\"label\":\"山顶罐区\",\"dataType\":262}]},{\"value\":223,\"parentId\":199,\"label\":\"前驱体维修C区\",\"dataType\":276,\"children\":[{\"value\":216,\"parentId\":223,\"label\":\"三元七车间\",\"dataType\":262},{\"value\":217,\"parentId\":223,\"label\":\"三元八车间\",\"dataType\":262},{\"value\":218,\"parentId\":223,\"label\":\"三元九车间\",\"dataType\":262},{\"value\":219,\"parentId\":223,\"label\":\"三元十车间\",\"dataType\":262},{\"value\":248,\"parentId\":223,\"label\":\"配料车间32\",\"dataType\":262},{\"value\":249,\"parentId\":223,\"label\":\"综合配套车间\",\"dataType\":262}]},{\"value\":232,\"parentId\":199,\"label\":\"质量部\",\"dataType\":268},{\"value\":233,\"parentId\":199,\"label\":\"技术部\",\"dataType\":268},{\"value\":241,\"parentId\":199,\"label\":\"维修A区\",\"dataType\":276}]},{\"value\":224,\"parentId\":220,\"label\":\"环保厂区\",\"dataType\":267,\"children\":[{\"value\":320,\"parentId\":224,\"label\":\"水循环维修区\",\"dataType\":276,\"children\":[{\"value\":226,\"parentId\":320,\"label\":\"水循环一车间\",\"dataType\":262},{\"value\":227,\"parentId\":320,\"label\":\"水循环二车间\",\"dataType\":262},{\"value\":228,\"parentId\":320,\"label\":\"水循环三车间\",\"dataType\":262},{\"value\":229,\"parentId\":320,\"label\":\"水循环四车间\",\"dataType\":262},{\"value\":230,\"parentId\":320,\"label\":\"水循环五车间\",\"dataType\":262},{\"value\":231,\"parentId\":320,\"label\":\"脱氨塔车间\",\"dataType\":262},{\"value\":321,\"parentId\":320,\"label\":\"大水池\",\"dataType\":262},{\"value\":322,\"parentId\":320,\"label\":\"环保设备【安环部】\",\"dataType\":262}]}]},{\"value\":225,\"parentId\":220,\"label\":\"原料厂区\",\"dataType\":267},{\"value\":316,\"parentId\":220,\"label\":\"设备部\",\"dataType\":268,\"children\":[{\"value\":317,\"parentId\":316,\"label\":\"设备管理科\",\"dataType\":269},{\"value\":318,\"parentId\":316,\"label\":\"厂区设备科\",\"dataType\":269},{\"value\":319,\"parentId\":316,\"label\":\"公共设备科\",\"dataType\":269},{\"value\":323,\"parentId\":316,\"label\":\"设备技术科\",\"dataType\":269}]},{\"value\":324,\"parentId\":220,\"label\":\"质量部\",\"dataType\":262,\"children\":[{\"value\":328,\"parentId\":324,\"label\":\"检测中心\",\"dataType\":269}]},{\"value\":325,\"parentId\":220,\"label\":\"检测中心\",\"dataType\":262,\"children\":[{\"value\":329,\"parentId\":325,\"label\":\"检测室\",\"dataType\":268,\"children\":[{\"value\":326,\"parentId\":329,\"label\":\"电性能检测班\",\"dataType\":268},{\"value\":327,\"parentId\":329,\"label\":\"仪器分析班\",\"dataType\":268},{\"value\":330,\"parentId\":329,\"label\":\"生产分析班\",\"dataType\":268},{\"value\":333,\"parentId\":329,\"label\":\"物性检测班\",\"dataType\":268},{\"value\":334,\"parentId\":329,\"label\":\"电性能检测班1\",\"dataType\":268},{\"value\":335,\"parentId\":329,\"label\":\"理化分析班\",\"dataType\":268}]},{\"value\":331,\"parentId\":325,\"label\":\"电性能检测室\",\"dataType\":262,\"children\":[{\"value\":332,\"parentId\":331,\"label\":\"电性能检测班\",\"dataType\":268}]},{\"value\":336,\"parentId\":325,\"label\":\"物性检测室\",\"dataType\":268,\"children\":[{\"value\":337,\"parentId\":336,\"label\":\"物性检测班\",\"dataType\":268}]}]}]}]}]");

		List<Object> list = JSONArray.parseArray(array.toJSONString(), Object.class);
		// 创建一个excel
		Workbook workbook = new XSSFWorkbook();
		workbook = cascade(workbook, 10, 5, 2, new String[] { "新仁集团" }, list,"label","children","sheetName");
		FileOutputStream os = null;
		try {
			os = new FileOutputStream("D:/1/" + GetRandomNum(1000, 9999) + ".xlsx");
			workbook.write(os);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			IOUtils.closeQuietly(os);
		}
	}

	public static int GetRandomNum(int num1, int num2) {
		int result = (int) (num1 + Math.random() * (num2 - num1 + 1));
		return result;
	}

	/**
	 *
	 * @param workbook
	 * @param rowNumber 渲染行数
	 * @param level	层级
	 * @param start	开始列
	 * @param parentArr	一级下拉框值
	 * @param list	树形列表
	 * @param pname	显示值  label
	 * @param children	子集字段名
	 * @param sheetName	sheet名称
	 * @return
	 */
	public static Workbook cascade(Workbook workbook, Integer rowNumber, Integer level, Integer start,
			String[] parentArr, List<Object> list,String pname,String children,String sheetName) {
		Map<String, String[]> map = tree(list, pname, children, new HashMap<>());
		String [] hasChild = arr(list, pname, children, new StringBuffer());
		// 创建需要用户填写的sheet
		XSSFSheet sheet = (XSSFSheet) workbook.createSheet(sheetName);

		// 创建一个专门用来存放地区信息的隐藏sheet页
		// 因此也不能在现实页之前创建，否则无法隐藏。
		Sheet hideSheet = workbook.createSheet("area");
		// 这一行作用是将此sheet隐藏，功能未完成时注释此行,可以查看隐藏sheet中信息是否正确
		workbook.setSheetHidden(workbook.getSheetIndex(hideSheet), true);

		int rowId = 0;
		// 设置第一行，存省的信息
		Row provinceRow = hideSheet.createRow(rowId++);
		provinceRow.createCell(0).setCellValue("渠道");

		for (int i = 0; i < parentArr.length; i++) {
			Cell provinceCell = provinceRow.createCell(i + 1);
			provinceCell.setCellValue(parentArr[i]);
		}
		// 将具体的数据写入到每一行中，行开头为父级区域，后面是子区域。
		for (int i = 0; i < hasChild.length; i++) {
			String key = hasChild[i];
			String[] son = map.get(key);
			Row row1 = hideSheet.createRow(rowId++);
			row1.createCell(0).setCellValue(key);
			for (int j = 0; j < son.length; j++) {
				Cell cell0 = row1.createCell(j + 1);
				cell0.setCellValue(son[j]);
			}

			// 添加名称管理器
			String range = getRange(1, rowId, son.length);
			Name name = workbook.createName();
			// key不可重复
			name.setNameName(key);
			String formula = "area!" + range;
			name.setRefersToFormula(formula);
		}

		XSSFDataValidationHelper dvHelper = new XSSFDataValidationHelper((XSSFSheet) sheet);
		// 省规则
		DataValidationConstraint provConstraint = dvHelper.createExplicitListConstraint(parentArr);
		// 四个参数分别是：起始行、终止行、起始列、终止列
		CellRangeAddressList provRangeAddressList = new CellRangeAddressList(1, rowNumber, start, start);
		DataValidation provinceDataValidation = dvHelper.createValidation(provConstraint, provRangeAddressList);
		// 验证
		provinceDataValidation.createErrorBox("error", "请选择正确的渠道");
		provinceDataValidation.setShowErrorBox(true);
		provinceDataValidation.setSuppressDropDownArrow(true);
		sheet.addValidationData(provinceDataValidation);

		// 对前20行设置有效性

		/**
		 * TODO 由于上方设置第一列的来源值为 渠道 所以j是从第二列开始设置 下拉选项来源 来源于当前行的上一列值
		 *
		 */
		for (int i = 2; i < rowNumber; i++) {
			for (int j = start + 1; j < level + start; j++) {
				setDataValidation(getAlphabet()[j - 1], sheet, i, j + 1);
			}
		}
		return workbook;
	}

	/**
	 * 设置有效性
	 *
	 * @param offset 主影响单元格所在列，即此单元格由哪个单元格影响联动
	 * @param sheet
	 * @param rowNum 行数
	 * @param colNum 列数
	 */
	public static void setDataValidation(String offset, XSSFSheet sheet, int rowNum, int colNum) {
		XSSFDataValidationHelper dvHelper = new XSSFDataValidationHelper(sheet);
		DataValidation data_validation_list;
		data_validation_list = getDataValidationByFormula("INDIRECT($" + offset + (rowNum) + ")", rowNum, colNum,
				dvHelper);
		sheet.addValidationData(data_validation_list);
	}

	/**
	 * 加载下拉列表内容
	 *
	 * @param formulaString
	 * @param naturalRowIndex
	 * @param naturalColumnIndex
	 * @param dvHelper
	 * @return
	 */
	private static DataValidation getDataValidationByFormula(String formulaString, int naturalRowIndex,
			int naturalColumnIndex, XSSFDataValidationHelper dvHelper) {
		// 加载下拉列表内容
		// 举例：若formulaString = "INDIRECT($A$2)" 表示规则数据会从名称管理器中获取key与单元格 A2 值相同的数据，
		// 如果A2是江苏省，那么此处就是江苏省下的市信息。
		XSSFDataValidationConstraint dvConstraint = (XSSFDataValidationConstraint) dvHelper
				.createFormulaListConstraint(formulaString);
		// 设置数据有效性加载在哪个单元格上。
		// 四个参数分别是：起始行、终止行、起始列、终止列
		int firstRow = naturalRowIndex - 1;
		int lastRow = naturalRowIndex - 1;
		int firstCol = naturalColumnIndex - 1;
		int lastCol = naturalColumnIndex - 1;
		CellRangeAddressList regions = new CellRangeAddressList(firstRow, lastRow, firstCol, lastCol);
		// 数据有效性对象
		// 绑定
		XSSFDataValidation data_validation_list = (XSSFDataValidation) dvHelper.createValidation(dvConstraint, regions);
		data_validation_list.setEmptyCellAllowed(false);
		if (data_validation_list instanceof XSSFDataValidation) {
			data_validation_list.setSuppressDropDownArrow(true);
			data_validation_list.setShowErrorBox(true);
		} else {
			data_validation_list.setSuppressDropDownArrow(false);
		}
		// 设置输入信息提示信息
		data_validation_list.createPromptBox("下拉选择提示", "请使用下拉方式选择合适的值！");
		// 设置输入错误提示信息
		// data_validation_list.createErrorBox("选择错误提示", "你输入的值未在备选列表中，请下拉选择合适的值！");
		return data_validation_list;
	}

	/**
	 * 计算formula
	 *
	 * @param offset   偏移量，如果给0，表示从A列开始，1，就是从B列
	 * @param rowId    第几行
	 * @param colCount 一共多少列
	 * @return 如果给入参 1,1,10. 表示从B1-K1。最终返回 $B$1:$K$1
	 */
	public static String getRange(int offset, int rowId, int colCount) {
		char start = (char) ('A' + offset);
		if (colCount <= 25) {
			char end = (char) (start + colCount - 1);
			return "$" + start + "$" + rowId + ":$" + end + "$" + rowId;
		} else {
			char endPrefix = 'A';
			char endSuffix = 'A';
			if ((colCount - 25) / 26 == 0 || colCount == 51) {// 26-51之间，包括边界（仅两次字母表计算）
				if ((colCount - 25) % 26 == 0) {// 边界值
					endSuffix = (char) ('A' + 25);
				} else {
					endSuffix = (char) ('A' + (colCount - 25) % 26 - 1);
				}
			} else {// 51以上
				if ((colCount - 25) % 26 == 0) {
					endSuffix = (char) ('A' + 25);
					endPrefix = (char) (endPrefix + (colCount - 25) / 26 - 1);
				} else {
					endSuffix = (char) ('A' + (colCount - 25) % 26 - 1);
					endPrefix = (char) (endPrefix + (colCount - 25) / 26);
				}
			}
			return "$" + start + "$" + rowId + ":$" + endPrefix + endSuffix + "$" + rowId;
		}
	}
	@SuppressWarnings("unchecked")
	public static String [] arr(List<Object> list, String pname, String childName, StringBuffer data) {
		StringBuffer result = data;
		for (Object m : list) {
			Map<String, Object> map = (Map<String, Object>) m;
			List<Object> children = (List<Object>) map.get(childName);
			if (children != null && !children.isEmpty()) {// 存在子集
				result.append(map.get(pname) + ",");
				arr(children, pname, childName, result);
			}
		}
		return result.toString().split(",");
	}

	/**
	 * @param list
	 * @param pname
	 * @param childName
	 * @param data
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static Map<String, String[]> tree(List<Object> list, String pname, String childName,
			Map<String, String[]> data) {
		Map<String, String[]> result = data;
		for (Object m : list) {
			Map<String, Object> map = (Map<String, Object>) m;
			String key = map.get(pname) + "";
			List<Object> children = (List<Object>) map.get(childName);
			if (children != null && !children.isEmpty()) {// 存在子集
				String value = "";
				for (Object c : children) {
					Map<String, Object> child = (Map<String, Object>) c;
					value += child.get(pname) + ",";
				}
				tree(children, pname, childName, result);
				result.put(key, value.split(","));
			}

		}
		return result;
	}

	public static String[] getAlphabet() {
		String alphabet = "";
		for (int i = 1; i <= 26; i++) {
			Character it = Character.toUpperCase((char) (96 + i));
			alphabet += it + ",";

		}
		return alphabet.split(",");
	}
}

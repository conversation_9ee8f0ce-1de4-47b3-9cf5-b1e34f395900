// 调试 printVueCode 方法的脚本
// 在浏览器控制台中运行此脚本来排查问题

console.log('=== printVueCode 调试脚本 ===');

// 1. 检查 Vue 实例
console.log('1. 检查 Vue 实例:');
if (typeof Vue !== 'undefined') {
  console.log('✓ Vue 已加载');
} else {
  console.log('✗ Vue 未加载');
}

// 2. 查找 VFormRender 组件实例
console.log('\n2. 查找 VFormRender 组件实例:');
function findVFormRenderInstances() {
  const instances = [];
  
  function traverse(vm) {
    if (vm.$options.name === 'VFormRender') {
      instances.push(vm);
    }
    if (vm.$children) {
      vm.$children.forEach(child => traverse(child));
    }
  }
  
  // 从根实例开始遍历
  if (window.app && window.app.$children) {
    window.app.$children.forEach(child => traverse(child));
  }
  
  return instances;
}

const vformInstances = findVFormRenderInstances();
console.log(`找到 ${vformInstances.length} 个 VFormRender 实例`);

vformInstances.forEach((instance, index) => {
  console.log(`\n实例 ${index + 1}:`);
  console.log('- 组件名称:', instance.$options.name);
  console.log('- printVueCode 方法存在:', typeof instance.printVueCode === 'function');
  console.log('- formConfig:', instance.formConfig);
  console.log('- widgetList 长度:', instance.widgetList ? instance.widgetList.length : 0);
  
  // 尝试调用方法
  if (typeof instance.printVueCode === 'function') {
    console.log('- 尝试调用 printVueCode...');
    try {
      const result = instance.printVueCode();
      console.log('✓ 调用成功');
    } catch (error) {
      console.log('✗ 调用失败:', error.message);
    }
  }
});

// 3. 检查常见的引用方式
console.log('\n3. 检查常见的引用方式:');

// 检查 $refs
if (window.app && window.app.$refs) {
  console.log('可用的 $refs:', Object.keys(window.app.$refs));
  
  Object.keys(window.app.$refs).forEach(refName => {
    const ref = window.app.$refs[refName];
    if (ref && ref.$options && ref.$options.name === 'VFormRender') {
      console.log(`✓ 找到引用: ${refName}`);
      console.log(`- printVueCode 方法存在:`, typeof ref.printVueCode === 'function');
    }
  });
}

// 4. 提供手动调用示例
console.log('\n4. 手动调用示例:');
if (vformInstances.length > 0) {
  console.log('您可以使用以下代码手动调用:');
  console.log(`
// 方法1: 直接调用第一个实例
window.vformInstance = ${JSON.stringify(vformInstances[0], null, 2)};
window.vformInstance.printVueCode();

// 方法2: 通过 $refs 调用 (如果存在)
// this.$refs.formRender.printVueCode();

// 方法3: 查找并调用
const instances = [];
function findVForm(vm) {
  if (vm.$options.name === 'VFormRender') instances.push(vm);
  if (vm.$children) vm.$children.forEach(child => findVForm(child));
}
findVForm(this.$root);
if (instances.length > 0) instances[0].printVueCode();
  `);
  
  // 将第一个实例暴露到全局
  window.debugVFormInstance = vformInstances[0];
  console.log('已将第一个实例暴露为 window.debugVFormInstance');
  console.log('您可以直接调用: window.debugVFormInstance.printVueCode()');
}

// 5. 常见问题检查
console.log('\n5. 常见问题检查:');

// 检查是否在正确的时机调用
console.log('- 当前页面 URL:', window.location.href);
console.log('- DOM 是否已加载:', document.readyState);

// 检查组件是否已挂载
vformInstances.forEach((instance, index) => {
  console.log(`- 实例 ${index + 1} 是否已挂载:`, instance._isMounted);
  console.log(`- 实例 ${index + 1} 是否已销毁:`, instance._isDestroyed);
});

console.log('\n=== 调试脚本结束 ===');

// 导出调试函数
window.debugPrintVueCode = function() {
  if (window.debugVFormInstance && typeof window.debugVFormInstance.printVueCode === 'function') {
    return window.debugVFormInstance.printVueCode();
  } else {
    console.error('没有可用的 VFormRender 实例');
  }
};

console.log('\n可用的调试函数:');
console.log('- window.debugPrintVueCode() - 调用 printVueCode 方法');

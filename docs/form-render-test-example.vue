<template>
  <div class="form-test-container">
    <div class="header">
      <h2>表单渲染器测试</h2>
      <div class="button-group">
        <el-button @click="printFormCode" type="primary" icon="el-icon-printer">
          打印Vue代码
        </el-button>
        <el-button @click="resetForm" type="default">
          重置表单
        </el-button>
        <el-button @click="getFormData" type="success">
          获取表单数据
        </el-button>
      </div>
    </div>

    <!-- 表单渲染组件 -->
    <div class="form-container">
      <v-form-render 
        ref="formRender" 
        :form-json="formJson" 
        :form-data="formData"
        @formDataChange="handleFormDataChange"
      />
    </div>

    <!-- 调试信息 -->
    <div class="debug-info" v-if="showDebug">
      <h3>调试信息</h3>
      <pre>{{ JSON.stringify(currentFormData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script>
import VFormRender from '@/components/form-render/index.vue'

export default {
  name: 'FormRenderTestExample',
  components: {
    VFormRender
  },
  data() {
    return {
      showDebug: false,
      currentFormData: {},
      formData: {
        // 初始表单数据
        userName: '',
        userAge: 0,
        userGender: '',
        userHobbies: [],
        userBirthday: '',
        isActive: false
      },
      // 示例表单JSON配置
      formJson: {
        formConfig: {
          labelPosition: 'left',
          labelWidth: 120,
          size: 'medium',
          customClass: 'test-form'
        },
        widgetList: [
          {
            id: 'userName',
            type: 'input',
            category: 'field',
            formItemFlag: true,
            options: {
              name: 'userName',
              label: '用户姓名',
              placeholder: '请输入用户姓名',
              required: true,
              defaultValue: '',
              validationHint: '请输入用户姓名'
            }
          },
          {
            id: 'userAge',
            type: 'number',
            category: 'field',
            formItemFlag: true,
            options: {
              name: 'userAge',
              label: '用户年龄',
              placeholder: '请输入年龄',
              required: true,
              defaultValue: 0,
              min: 0,
              max: 120
            }
          },
          {
            id: 'userGender',
            type: 'radio',
            category: 'field',
            formItemFlag: true,
            options: {
              name: 'userGender',
              label: '性别',
              required: true,
              defaultValue: '',
              optionItems: [
                { label: '男', value: 'male' },
                { label: '女', value: 'female' }
              ]
            }
          },
          {
            id: 'userHobbies',
            type: 'checkbox',
            category: 'field',
            formItemFlag: true,
            options: {
              name: 'userHobbies',
              label: '兴趣爱好',
              defaultValue: [],
              optionItems: [
                { label: '读书', value: 'reading' },
                { label: '运动', value: 'sports' },
                { label: '音乐', value: 'music' },
                { label: '旅行', value: 'travel' }
              ]
            }
          },
          {
            id: 'userBirthday',
            type: 'date',
            category: 'field',
            formItemFlag: true,
            options: {
              name: 'userBirthday',
              label: '出生日期',
              placeholder: '请选择出生日期',
              defaultValue: ''
            }
          },
          {
            id: 'isActive',
            type: 'switch',
            category: 'field',
            formItemFlag: true,
            options: {
              name: 'isActive',
              label: '是否激活',
              defaultValue: false
            }
          }
        ]
      }
    }
  },
  mounted() {
    // 确保组件挂载完成
    this.$nextTick(() => {
      console.log('FormRender组件引用:', this.$refs.formRender);
      console.log('printVueCode方法存在:', typeof this.$refs.formRender?.printVueCode === 'function');
    });
  },
  methods: {
    // 打印表单代码 - 主要方法
    printFormCode() {
      console.log('开始打印表单代码...');
      
      // 方法1: 基础检查
      if (!this.$refs.formRender) {
        this.$message.error('表单组件未找到，请确保组件已正确加载');
        return;
      }

      // 方法2: 检查方法是否存在
      if (typeof this.$refs.formRender.printVueCode !== 'function') {
        this.$message.error('printVueCode 方法不存在，请检查组件版本');
        return;
      }

      // 方法3: 使用 try-catch 包装
      try {
        const vueCode = this.$refs.formRender.printVueCode();
        console.log('代码生成成功');
        this.$message.success('Vue代码已生成，请查看新窗口或控制台');
        return vueCode;
      } catch (error) {
        console.error('打印代码失败:', error);
        this.$message.error('打印代码失败: ' + error.message);
      }
    },

    // 备用方法：延迟调用
    printFormCodeDelayed() {
      this.$nextTick(() => {
        setTimeout(() => {
          this.printFormCode();
        }, 100);
      });
    },

    // 重置表单
    resetForm() {
      if (this.$refs.formRender && this.$refs.formRender.resetForm) {
        this.$refs.formRender.resetForm();
        this.$message.success('表单已重置');
      }
    },

    // 获取表单数据
    async getFormData() {
      if (this.$refs.formRender && this.$refs.formRender.getFormData) {
        try {
          const formData = await this.$refs.formRender.getFormData();
          console.log('表单数据:', formData);
          this.currentFormData = formData;
          this.showDebug = true;
          this.$message.success('表单数据获取成功，请查看调试信息');
        } catch (error) {
          console.error('获取表单数据失败:', error);
          this.$message.error('获取表单数据失败: ' + error);
        }
      }
    },

    // 表单数据变化处理
    handleFormDataChange(data) {
      console.log('表单数据变化:', data);
      this.currentFormData = data.formDataModel;
    }
  }
}
</script>

<style lang="scss" scoped>
.form-test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;

    h2 {
      margin: 0;
      color: #333;
    }

    .button-group {
      .el-button {
        margin-left: 10px;
      }
    }
  }

  .form-container {
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .debug-info {
    margin-top: 20px;
    padding: 15px;
    background: #f5f5f5;
    border-radius: 4px;
    border-left: 4px solid #409EFF;

    h3 {
      margin-top: 0;
      color: #409EFF;
    }

    pre {
      background: #fff;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
      font-size: 12px;
    }
  }
}

::v-deep .test-form {
  .el-form-item {
    margin-bottom: 20px;
  }
}
</style>

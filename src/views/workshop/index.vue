<template>
  <div class="container">
    <div class="top">
      <div class="left d-flex a-center mr-l5">
        <a href="/workStation">
          <img src="@/assets/favicon.png" alt="" />
        </a>
        <span>南岭机械厂</span>
      </div>
      <div class="center">工厂维修进度看板</div>
      <div class="right d-flex a-center j-end mr-r5">
        <!-- 新加按钮切换车间 -->
        <div style="margin-right: 15px;">
          <el-radio-group v-model="shop" @change="shopChange">
            <el-radio-button label="全部"></el-radio-button>
            <el-radio-button label="总装"></el-radio-button>
            <el-radio-button label="光电"></el-radio-button>
          </el-radio-group>
        </div>
        <el-date-picker
          v-model="date"
          type="month"
          value-format="yyyy-MM"
          format="yyyy 年 MM 月"
          placeholder="选择月"
          @change="getData"
        >
        </el-date-picker>
      </div>
    </div>
    <div class="middle">
      <div>
        <div class="left">
          <div class="label">本月已完成车辆</div>
          <div class="d-flex a-center mr-t10">
            <div class="val">{{carNumber.finishRepair}}</div>
            <div>台</div>
          </div>
        </div>
        <div class="right">
          <div style="width:100%;text-align:center">
            <div v-for="(pair, index) in finishPairs" :key="index" class="pair-row">
              <div class="left-side">
                {{ index * 2 + 1 }}. [ {{ pair[0] }} ]
              </div>
              <div class="right-side" v-if="pair[1]">
                {{ index * 2 + 2 }}. [ {{ pair[1] }} ]
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div class="left">
          <div class="label">本月维修中车辆</div>
          <div class="d-flex a-center mr-t10">
            <div class="val">{{carNumber.waitRepair}}</div>
            <div>台</div>
          </div>
        </div>
        <div class="right">
         <div style="width:100%;text-align:center">
           <div v-for="(pair, index) in doingPairs" :key="index" class="pair-row">
             <div class="left-side">
               {{ index * 2 + 1 }}. [ {{ pair[0] }} ]
             </div>
             <div class="right-side" v-if="pair[1]">
               {{ index * 2 + 2 }}. [ {{ pair[1] }} ]
             </div>
           </div>
         </div>
        </div>
      </div>

<!--      <div class="d-flex">
        <div class="left label">车辆维修达成率</div>
        <div style="width: 75%; height: 100%">
          <v-chart
            :options="pieOptions"
            style="width: 100%; height: 100%"
            autoresize
          />
        </div>
      </div>-->
    </div>
    <div class="bottom">
      <div style="display: flex;">
        <div id="title" class="title">{{shop}}车间维修车辆详细进度反馈</div>
        <div ref="colorContainer" class="color-container">
          <div class="color-box">
            <div class="color-rect1"></div>
            <p class="color-label">进行中</p>
          </div>
          <div class="color-box">
            <div class="color-rect2"></div>
            <p class="color-label">正常完成</p>
          </div>
          <div class="color-box">
            <div class="color-rect3"></div>
            <p class="color-label">超时完成</p>
          </div>
          <div class="color-box">
            <div class="color-rect4"></div>
            <p class="color-label">超时未完成</p>
          </div>
        </div>
      </div>

      <div class="workshop" v-if="shop == '全部'">
        <div class="half-text workshop-left">总装车间</div>
        <div class="half-text workshop-right">光电车间</div>
      </div>
      <!-- height: calc(100% - 30px);
      // overflow: auto; -->
      <div id="list" class="list" :style="{
                height: 'calc(100% - ' + (shop == '全部'? '64' : '30') + 'px)',
                overflow: 'auto'
              }">
        <div class="row" v-for="(item, key) in statusList" :key="key">
          <div class="item" v-if="shop == '总装' || shop == '全部'"
            :style="{
                width: shop == '全部'? '49.5%' : '100%'
              }">
            <div class="label" :style="{width: shop == '全部'? '20%' : '13%'}">{{ key }}</div>
            <div class="flow" :style="{width: shop == '全部'? '80%' : '87%'}">
              <div class="top-line"></div>
              <div class="right-line"></div>
              <div class="bottom-line"></div>
              <div
                class="flow-item"
                v-for="(it, index) in item['1']"
                :key="index"
                :style="{
                  left: positionMap[index].left,
                  top: positionMap[index].top,
                  bottom: positionMap[index].bottom,
                  transform: positionMap[index].transform,
                  width: (shop == '全部' ? '80px' : '100px'),
                  height: (shop == '全部' ? '30px' : '40px')
                }"
              >
                <!--        3超时未完成 2超时已完成 1正常完成 0进行中        -->
                <div class="box" @click="selectProcess(key,it)"
                  :class="[it.status=='3'?'current':it.status=='2'?'out':it.status=='1'?'success':'']"
                  :style="{'font-size': (shop == '全部' ? '12px' : '16px')}">
                  <span>{{ it.name }}</span>
                  <div class="time" :style="{'font-size': (shop == '全部' ? '12px' : '15px')}">{{ it.planTime || '- -' }}</div>
                  <div class="finishRate" :style="{'font-size': (shop == '全部' ? '12px' : '15px')}">{{ it.finishRate || '- -' }}</div>
                </div>
              </div>
            </div>
          </div>

          <div class="item" v-if="shop == '光电' || shop == '全部'"
            :style="{
                width: shop == '全部'? '49.5%' : '100%'
              }">
            <div class="label" :style="{width: shop == '全部'? '20%' : '13%'}">{{ key }}</div>
            <div class="flow" :style="{width: shop == '全部'? '80%' : '87%'}">
              <div class="top-line"></div>
              <div class="right-line"></div>
              <div class="bottom-line" style="width: 64%; left: 27%"></div>
              <div
                class="flow-item"
                v-for="(it, index) in item['2']"
                :key="index"
                :style="{
                  left: positionMap[index].left,
                  top: positionMap[index].top,
                  bottom: positionMap[index].bottom,
                  transform: positionMap[index].transform,
                  width: (shop == '全部' ? '80px' : '100px'),
                  height: (shop == '全部' ? '30px' : '40px')
                }"
              >
                <!--        3超时未完成 2超时已完成 1正常完成 0进行中        -->
                <div class="box"  @click="selectProcess(key,it)"
                  :class="[it.status=='3'?'current':it.status=='2'?'out':it.status=='1'?'success':'']"
                  :style="{'font-size': (shop == '全部' ? '12px' : '16px')}"
                >
                  <span>{{ it.name }}</span>
                  <div class="time" :style="{'font-size': (shop == '全部' ? '12px' : '15px')}">{{ it.planTime|| '- -' }}</div>
                  <div class="finishRate" :style="{'font-size': (shop == '全部' ? '12px' : '15px')}">{{ it.finishRate || '- -' }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
    <el-dialog
      :visible="checkShow"
      :title="title"
      @close="checkShow = false"
      width="95%"
      append-to-body
    >
      <v-form-render
        v-if="formJson"
        ref="vFormRefLast"
        :form-json="formJson"
        :form-data="initData"
        :writeState="false"
        :key="randomKey"
      />
    </el-dialog>
  </div>
</template>

<script>
import moment from "moment";
import { getByTemplateIdInfo } from '@/api/tool/form'
export default {
  data() {
    return {
      title: '',
      randomKey: '',
      formJson: '',
      initData: {},
      keyToMap: {},
      aliasToMap: {},
      checkShow: false,
      templateId: '',
      templateName: '',
      message: "",
      lineOptions: {},
      pieOptions: {},
      currentTab: 0,
      currentShop: 1,
      tabList: [],
      carNumber: {},
      statusList: {},
      positionMap: [
        {
          left: "0",
          bottom: "",
          top: "25%",
          transform: "translate(-50%,-50%)",
        },
        {
          left: "13%",
          bottom: "",
          top: "25%",
          transform: "translate(-50%,-50%)",
        },
        {
          left: "26%",
          bottom: "",
          top: "25%",
          transform: "translate(-50%,-50%)",
        },
        {
          left: "39%",
          bottom: "",
          top: "25%",
          transform: "translate(-50%,-50%)",
        },
        {
          left: "52%",
          bottom: "",
          top: "25%",
          transform: "translate(-50%,-50%)",
        },
        {
          left: "65%",
          bottom: "",
          top: "25%",
          transform: "translate(-50%,-50%)",
        },
        {
          left: "78%",
          bottom: "",
          top: "25%",
          transform: "translate(-50%,-50%)",
        },
        {
          left: "91%",
          bottom: "",
          top: "25%",
          transform: "translate(-50%,-50%)",
        },
        {
          left: "91%",
          bottom: "25%",
          top: "",
          transform: "translate(-50%,50%)",
        },
        {
          left: "78%",
          bottom: "25%",
          top: "",
          transform: "translate(-50%,50%)",
        },
        {
          left: "65%",
          bottom: "25%",
          top: "",
          transform: "translate(-50%,50%)",
        },
        {
          left: "52%",
          bottom: "25%",
          top: "",
          transform: "translate(-50%,50%)",
        },
        {
          left: "39%",
          bottom: "25%",
          top: "",
          transform: "translate(-50%,50%)",
        },
        {
          left: "26%",
          bottom: "25%",
          top: "",
          transform: "translate(-50%,50%)",
        },
        {
          left: "13%",
          bottom: "25%",
          top: "",
          transform: "translate(-50%,50%)",
        },
        {
          left: "0",
          bottom: "25%",
          top: "",
          transform: "translate(-50%,50%)",
        },
      ],
      finishCarList: [],
      waitCarList: [],
      finishPairs: [],
      doingPairs: [],
      date: "",
      shop: "全部"
    };
  },
  mounted() {
    this.alignColorContainer();
    this.date = moment(new Date()).format("yyyy-MM");
    this.getData();
  },
  computed: {
    month() {
      return new Date().getMonth() + 1;
    },
  },
  methods: {
    alignColorContainer() {
      const targetDiv = document.getElementById('title');

      const colorContainer = this.$refs.colorContainer;

      // 计算目标div的底部位置
      const targetBottom = targetDiv.getBoundingClientRect().top;

      // 设置颜色块容器的位置
      // colorContainer.style.position = 'absolute';
      // colorContainer.style.top = `${targetBottom}px`;
      // colorContainer.style.right = '0';
    },
    selectProcess(carCode,it){
      console.log('press~', carCode, it)
      this.$refs.vFormRefLast.printVueCode()
      //this.getFormJsonDialog(this.templateId, this.templateName, { carCode: carCode, code: it.code, type: it.type, planTime: it.planTime })
    },
    getFormJsonDialog(templateId, title, param) {
      this.title = title
      getByTemplateIdInfo(templateId).then((res) => {
        this.checkShow = true
        var func = (templateJson) => {
          // 迭代出别名和elementkey
          let cr = (arr) => {
            arr.forEach((item) => {
              // 如果有别名 设置别名
              if (item.options.alias) {
                this.keyToMap[item.options.alias] = item.id
                this.aliasToMap[item.id] = item.options.alias
              }
              if (item.cols && item.cols.length > 0) {
                cr(item.cols)
              }
              if (item.widgetList && item.widgetList.length > 0) {
                cr(item.widgetList)
              }
            })
          }
          //迭代
          cr(templateJson.widgetList)

          Object.getOwnPropertyNames(this.initData)
            .filter((item) => item != '__ob__')
            .forEach((name) => {
              if (
                name.includes('subform') ||
                (this.keyToMap[name] && this.keyToMap[name].includes('subform'))
              ) {
                this.initData[name].forEach((it) => {
                  for (let k in it) {
                    if (this.keyToMap[k]) {
                      it[this.keyToMap[k]] = it[k]
                      delete it[k]
                    }
                  }
                })
              }
              if (this.keyToMap[name]) {
                this.initData[this.keyToMap[name]] = this.initData[name]
                delete this.initData[name]
              }
            })
          this.formJson = templateJson
        }
        this.initData = param

        if (res.data.templateJson) {
          let templateJson = JSON.parse(res.data.templateJson)
          func(templateJson)
          this.$nextTick(() => {
            this.randomKey = Math.random()
          })
        }
      })
    },
    getData() {
      this.finishPairs = [];
      this.doingPairs = [];
      this.getNumber();
      this.getCarList();
      this.getStatus();
    },
    interval() {
      if (this.timer) {
        clearInterval(this.timer);
      }
      this.currentTab = 0;
      this.timer = setInterval(() => {
        if (this.currentTab < this.tabList.length - 1) {
          this.currentTab++;
        } else {
          this.currentTab = 0;
        }
        this.updateStatus();
      }, 30000);
      this.$once("hook:beforeDestroy", () => {
        clearInterval(this.timer);
      });
    },

    getNumber() {
      this.$api({
        url: "/interfaces/view/planExec/getRepairCarInfo",
        params: {
          date: this.date,
        },
      }).then((res) => {
        this.carNumber = res.data;
      });
    },
    getCarList() {
      this.$api({
        url: "/interfaces/view/planExec/finishAndWaitList",
        params: {
          date: this.date,
        },
      }).then((res) => {
        this.finishCarList = res.data.finish
        this.waitCarList = res.data.wait //wait更改为进行中 参数名未更改
        for (let i = 0; i < this.finishCarList.length; i += 2) {
          this.finishPairs.push([this.finishCarList[i], this.finishCarList[i + 1] || null])
        }

        for (let i = 0; i < this.waitCarList.length; i += 2) {
          this.doingPairs.push([this.waitCarList[i], this.waitCarList[i + 1] || null])
        }
      });
    },

    getStatus() {
      this.$api({
        url: "/interfaces/view/dispatchMeeting/getProcessSeschedule",
        params: {
          date: this.date,
        },
      }).then((res) => {
        this.statusList = res.data.data;
        this.templateId = res.data.templateId;
        this.templateName = res.data.templateName;
      });
    },
    updateStatus() {
      this.getStatus();
    },
    shopChange(e) {
      this.shop = e;
    }
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-input__inner {
  background: #1c1f27 !important;
  border-color: #919295 !important ;
}

.color-container {
  display: flex;
  justify-content: flex-end; /* 水平分布 */
  align-items: center; /* 垂直居中（如果需要的话） */
  padding-bottom: 10px; /* 添加一些内边距 */
  padding-right: 30px;
  width: 17%;
}

.color-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-left: 10px; /* 颜色块之间的间距 */
  padding-right: 10px;
}

.color-rect1 {
  width: 30px; /* 矩形宽度 */
  height: 10px; /* 矩形高度 */
  margin-bottom: 5px; /* 矩形和标签之间的间距 */
  background-color: #bbbbbb;
}

.color-rect2 {
  width: 30px; /* 矩形宽度 */
  height: 10px; /* 矩形高度 */
  margin-bottom: 5px; /* 矩形和标签之间的间距 */
  background-color: #43a908;
}

.color-rect3 {
  width: 30px; /* 矩形宽度 */
  height: 10px; /* 矩形高度 */
  margin-bottom: 5px; /* 矩形和标签之间的间距 */
  background-color: #cdb554;
}

.color-rect4 {
  width: 30px; /* 矩形宽度 */
  height: 10px; /* 矩形高度 */
  margin-bottom: 5px; /* 矩形和标签之间的间距 */
  background-color: #c90303;
}

.color-label {
  font-weight: bold; /* 加粗标签字体 */
  font-size: 12px;
}


.workshop {
  display: flex;
  justify-content: space-between; /* 使左右两边元素分别靠近容器的左右两边 */
  align-items: center; /* 使元素在垂直方向上居中 */

}

.half-text {
  flex: 1; /* 平均分配剩余空间 */
  text-align: center; /* 文本水平居中 */
  font-weight: bold; /* 加粗字体 */
  font-size: 26px;
}

.workshop-left {
  //background-color: lightblue; /* 为了可视化效果，可以添加背景色 */
}

.workshop-right {
  //background-color: lightblue; /* 为了可视化效果，可以添加背景色 */
}


.pair-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-side,
.right-side {
  display: flex;
  align-items: center;
}

.left-side {
  margin-left: 10px;
  justify-content: flex-start;
}

.right-side {
  justify-content: flex-start; /* 确保内容左对齐 */
  min-width: 45%; /* 设置最小宽度，或根据需要调整 */
}

.container {
  background: #171e42;
  color: #fff;
  height: 100%;
  .top {
    height: 8%;
    display: flex;
    align-items: center;
    background: #121c51;
    width: 100%;
    position: relative;
    padding: 0 10px;
    .left,
    .right {
      width: 35%;
      font-size: 22px;
    }
    .left {
      img {
        width: 85px;
        height: 65px;
        margin-right: 5px;
      }
    }
    .center {
      height: 100%;
      font-size: 32px;
      width: 30%;
      background-image: url("./top.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      display: flex;
      padding-top: 1%;
      font-weight: bold;
      justify-content: center;
    }
    .center-bot {
      position: absolute;
      width: 30%;
      height: 15px;
      border: 2px solid #919295;
      border-radius: 0 0 5px 5px;
      border-top: none;
      z-index: 10;
      left: 50%;
      bottom: 0;
      background: #1c1f27;
      transform: translate(-50%, 14px);
    }
  }
  .middle {
    margin-left: 3%;
    width: 97%;
    display: flex;
    padding: 10px;
    height: 20%;
    padding-top: 30px;
    justify-content: space-between;
    align-items: center;
    & > div {
      width: 50%;
      display: flex;
      height: 100%;
      .left {
        width: 30%;
        font-size: 20px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
      .right {
        width: 60%;
        height: 100%;
        border: 2px solid #5d5f64;
        display: flex;
        justify-content: center;
        //align-items: center;
        //padding: 15px;
        color: #e7c50d;
        font-size: 16px;
        font-weight: bold;
        //overflow: hidden;
        overflow-y: auto; /* 超出高度时启用滚动条 */
      }
      .label {
        font-weight: bold;
      }

      .val {
        color: #e7c50d;
        padding: 3px 15px;
        border: 1px solid #5d5f64;
        width: 80px;
        text-align: center;
        font-size: 20px;
        font-weight: bold;
        margin-right: 10px;
      }
    }
  }
  .bottom {
    height: 70%;
    margin: 15px 10px;
    border: 2px solid #919295;
    padding: 10px;
    .title {
      font-size: 20px;
      width: 83%;
      text-align: center;
      height: 30px;
      line-height: 30px;
      padding-bottom: 10px;
      padding-left: 17%;
    }
    .list {
      // height: calc(100% - 30px);
      // overflow: auto;
      padding-top: 10px;
      .row {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        .item {
          // width: 100%;
          height: 180px;
          background: #415058;
          border: 1px solid #636e74;
          padding: 0 5px;
          display: flex;
          align-items: center;
          .label {
            font-size: 18px;
            font-weight: bold;
            // width: 13%;
          }
          .flow {
            height: 100%;
            // width: 87%;
            padding-left: 20px;
            position: relative;
            .top-line {
              position: absolute;
              top: 25%;
              left: 0;
              width: 91%;
              height: 2px;
              background: #919295;
            }
            .right-line {
              position: absolute;
              top: 25%;
              right: 9%;
              width: 2px;
              height: 50%;
              background: #919295;
            }
            .bottom-line {
              position: absolute;
              bottom: 25%;
              left: 0;
              width: 91%;
              height: 2px;
              background: #919295;
            }
            .flow-item {
              // width: 100px;
              // height: 40px;
              position: absolute;
              transform: translate(-50%, -50%);
              .box {
                width: 100%;
                height: 100%;
                position: relative;
                background: #bbbbbb;
                color: #fff;
                display: flex;
                justify-content: center;
                align-items: center;
                // font-size: 16px;
                cursor: pointer;
                &.success {
                  background: #43a908;
                }
                &.current {
                  background: #c90303;
                }
                &.out {
                  background: #cdb554;
                }
              }
              .time {
                position: absolute;
                top: -20px;
                left: 0;
                // font-size: 15px;
                text-align: center;
                width: 100%;
              }
              .finishRate {
                position: absolute;
                bottom: -20px;
                left: 0;
                // font-size: 15px;
                text-align: center;
                width: 100%;
              }
            }
          }
        }
      }
    }
  }
}
</style>

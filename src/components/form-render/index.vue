<template>
  <el-form
    ref="renderForm"
    :label-position="labelPosition"
    :size="size"
    :class="[customClass]"
    class="render-form"
    :label-width="labelWidth"
    :validate-on-rule-change="false"
    :model="formDataModel"
    @submit.native.prevent
  >
    <template v-for="(widget, index) in widgetList">
      <template v-if="'container' === widget.category">
        <component
          :is="getContainerWidgetName(widget)"
          :key="widget.id"
          :widget="widget"
          :parent-list="widgetList"
          :index-of-parent-list="index"
          :parent-widget="null"
        />
      </template>
      <template v-else>
        <component
          :is="getWidgetName(widget)"
          :key="widget.id"
          :field="widget"
          :form-model="formDataModel"
          :designer="null"
          :parent-list="widgetList"
          :index-of-parent-list="index"
          :parent-widget="null"
        />
      </template>
    </template>
  </el-form>
</template>

<script>
// import ElForm from 'element-ui/packages/form/src/form.vue'  /* 用于源码调试Element UI */
import emitter from 'element-ui/lib/mixins/emitter';
import './container-item/index';
import './popup-item/index';
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index';
import {
  deepClone,
  insertCustomCssToHead,
  insertGlobalFunctionsToHtml,
} from '../../utils/util';
import i18n, { changeLocale } from '../../utils/i18n';
import bus from '@/scripts/bus.js';
import request from '@/utils/request';
import { getToken } from '@/utils/auth';
export default {
  name: 'VFormRender',
  mixins: [emitter, i18n],
  components: {
    // ElForm,
    ...FieldComponents,
  },
  provide() {
    return {
      refList: this.widgetRefList,
      sfRefList: this.subFormRefList, // 收集SubForm引用
      formConfig: this.formConfig,
      globalOptionData: this.optionData,
      globalModel: {
        formModel: this.formDataModel,
      },
      previewState: this.previewState,
      writeState: this.writeState,
    };
  },
  watch: {
    formDataModel: {
      immediate: true,
      handler(old, val) {
        for (let key in val) {
          if (key.includes('select') && val[key] instanceof Array) {
            val[key] = val[key].join();
          }
        }
        this.$emit('handelFormData', val);
      },
      deep: true,
    },
  },
  props: {
    formJson: Object, // prop传入的表单JSON配置
    formData: {
      // prop传入的表单数据
      Object,
      default: () => {},
    },
    optionData: {
      // prop传入的选项数据
      type: Object,
      default: () => {},
    },
    previewState: {
      // 是否表单预览状态
      type: Boolean,
      default: false,
    },
    writeState: {
      // 是否表单预览状态
      type: Boolean,
      default: true,
    },
    hasPer: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      popupName: 'device-table',
      formJsonObj: this.formJson,
      openDialogTable: false,
      formDataModel: {
        //
      },
      asData: {
        // 别名数据
      },
      asMapping: {},

      widgetRefList: {},
      subFormRefList: {},
      dialogData: {
        fieldName: '',
        fieldModel: '',
      },
      dialogDataList: {},
    };
  },
  computed: {
    formConfig() {
      return this.formJsonObj.formConfig;
    },

    widgetList() {
      return this.formJsonObj.widgetList;
    },

    labelPosition() {
      if (!!this.formConfig && !!this.formConfig.labelPosition) {
        return this.formConfig.labelPosition;
      }

      return 'left';
    },

    labelWidth() {
      if (!!this.formConfig && !!this.formConfig.labelWidth) {
        return this.formConfig.labelWidth + 'px';
      }

      return '80px';
    },
    fieldName() {
      return this.dialogData.fieldName;
    },
    size() {
      if (!!this.formConfig && !!this.formConfig.size) {
        return this.formConfig.size;
      }

      return 'medium';
    },

    customClass() {
      return !!this.formConfig && !!this.formConfig.customClass
        ? this.formConfig.customClass
        : '';
    },
  },
  created() {
    // console.log('dialogDataList.optionItems==',dialogDataList.optionItems)
    this.buildFormModel(!this.formJsonObj ? null : this.formJsonObj.widgetList);
    this.initFormObject();
  },
  mounted() {
    this.initLocale();
    this.handleOnMounted();
    // 界面监听扫码
    this.emitScan();
  },
  methods: {
    $getValue(id) {
      return this.getWidgetRef(id) && this.getWidgetRef(id).getValue();
    },
    $setValue(id, value) {
      if (this.getWidgetRef(id)) {
        this.getWidgetRef(id).setValue(value);
      }
    },
    getRequest(pp) {
      return request(pp);
    },
    waitHttp(apiId, data) {
      let settings = {
        url: `${process.env.VUE_APP_BASE_API}/interfaces/magicInterfaces/execute`,
        type: 'post',
        timeout: 0,
        data: JSON.stringify({ apiId: apiId, body: data }),
        dataType: 'JSON',
        contentType: 'application/json;charset=UTF-8',
        headers: {
          Accept: 'application/json, text/plain, */*',
          Connection: 'keep-alive',
        },
        async: false,
      };
      // 是否需要设置 token
      const isToken = (settings.headers || {}).isToken === false;
      // 请求携带来自终端类型
      settings.headers['terminalType'] = 'PC';

      if (getToken() && !isToken) {
        settings.headers['Authorization'] = 'Bearer ' + getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
      }
      return $.ajax(settings).responseJSON;
    },
    emitScan() {
      // 监听按键
      var code = '';
      var lastTime, nextTime; // 上次时间、最新时间
      var lastCode, nextCode; // 上次按键、最新按键
      document.onkeypress = (e) => {
        // 获取按键
        if (window.event) {
          // IE
          nextCode = e.keyCode;
        } else if (e.which) {
          // Netscape/Firefox/Opera
          nextCode = e.which;
        }
        // 如果触发了回车事件(扫码结束时间)
        if (nextCode === 13) {
          if (code.length < 3) return; // 手动输入的时间不会让code的长度大于2，所以这里只会对扫码枪有

          this.handleOnScanCommit(code); // 获取到扫码枪输入的内容，做别的操作

          code = '';
          lastCode = '';
          lastTime = '';
          return;
        }
        nextTime = new Date().getTime(); // 记录最新时间
        if (!lastTime && !lastCode) {
          // 如果上次时间和上次按键为空
          code += e.key; // 执行叠加操作
        }
        // 如果有上次时间及上次按键
        if (lastCode && lastTime && nextTime - lastTime > 30) {
          // 当扫码前有keypress事件时,防止首字缺失
          code = e.key;
        } else if (lastCode && lastTime) {
          code += e.key;
        }
        lastCode = nextCode;
        lastTime = nextTime;
      };
    },
    initFormObject() {
      this.insertCustomStyleAndScriptNode();
      // this.buildFormModel()
      this.addFieldChangeEventHandler();
      this.registerFormToRefList();
      this.handleOnCreated();
    },

    getContainerWidgetName(widget) {
      return widget.type + '-item';
    },
    getPopupWidgetName() {
      return this.popupName + '-popup';
    },
    getWidgetName(widget) {
      return widget.type + '-widget';
    },

    initLocale() {
      let curLocale = localStorage.getItem('v_form_locale') || 'zh-CN';
      this.changeLanguage(curLocale);
    },

    insertCustomStyleAndScriptNode() {
      if (!!this.formConfig && !!this.formConfig.cssCode) {
        insertCustomCssToHead(this.formConfig.cssCode);
      }

      if (!!this.formConfig && !!this.formConfig.functions) {
        insertGlobalFunctionsToHtml(this.formConfig.functions);
      }
    },

    buildFormModel(widgetList) {
      if (!!widgetList && widgetList.length > 0) {
        widgetList.forEach((wItem) => {
          this.buildDataFromWidget(wItem);
        });
      }
    },

    buildDataFromWidget(wItem) {
      if (wItem.category === 'container') {
        if (wItem.type === 'grid') {
          if (!!wItem.cols && wItem.cols.length > 0) {
            wItem.cols.forEach((childItem) => {
              this.buildDataFromWidget(childItem);
            });
          }
        } else if (wItem.type === 'table') {
          if (!!wItem.rows && wItem.rows.length > 0) {
            wItem.rows.forEach((rowItem) => {
              if (!!rowItem.cols && rowItem.cols.length > 0) {
                rowItem.cols.forEach((colItem) => {
                  this.buildDataFromWidget(colItem);
                });
              }
            });
          }
        } else if (wItem.type === 'tab') {
          if (!!wItem.tabs && wItem.tabs.length > 0) {
            wItem.tabs.forEach((tabItem) => {
              if (!!tabItem.widgetList && tabItem.widgetList.length > 0) {
                tabItem.widgetList.forEach((childItem) => {
                  this.buildDataFromWidget(childItem);
                });
              }
            });
          }
        } else if (wItem.type === 'trends-tab') {
          let subFormName = wItem.options.name;
          if (!this.formData.hasOwnProperty(subFormName)) {
            let subFormDataRow = {};
            wItem.widgetList.forEach((subFormItem) => {
              if (subFormItem.category === 'container') {
                subFormItem.cols.forEach((childFormItem) => {
                  childFormItem.widgetList.forEach((childChildFormItem) => {
                    subFormDataRow[childChildFormItem.options.name] =
                      childChildFormItem.options.defaultValue;
                  });
                });
              } else {
                subFormDataRow[subFormItem.options.name] =
                  subFormItem.options.defaultValue;
              }
            });
            subFormDataRow['title'] = wItem.options.label;
            this.$set(this.formDataModel, subFormName, [subFormDataRow]); //
          } else {
            let initialValue = this.formData[subFormName];
            this.$set(this.formDataModel, subFormName, deepClone(initialValue));
          }
        } else if (wItem.type === 'sub-form') {
          let subFormName = wItem.options.name;
          if (!this.formData.hasOwnProperty(subFormName)) {
            let subFormDataRow = {};
            if (wItem.options.showBlankRow) {
              wItem.widgetList.forEach((subFormItem) => {
                if (subFormItem.formItemFlag) {
                  subFormDataRow[subFormItem.options.name] =
                    subFormItem.options.defaultValue;
                }
              });
              this.$set(this.formDataModel, subFormName, [subFormDataRow]); //
            } else {
              this.$set(this.formDataModel, subFormName, []); //
            }
          } else {
            let initialValue = this.formData[subFormName];
            this.$set(this.formDataModel, subFormName, deepClone(initialValue));
          }
        } else if (wItem.type === 'grid-col' || wItem.type === 'table-cell') {
          if (!!wItem.widgetList && wItem.widgetList.length > 0) {
            wItem.widgetList.forEach((childItem) => {
              this.buildDataFromWidget(childItem);
            });
          }
        } else if (wItem.type === 'data-table') {
          let subFormName = wItem.options.name;
          this.$set(this.formDataModel, subFormName, []); //
        } else {
          // 自定义容器组件
          if (!!wItem.widgetList && wItem.widgetList.length > 0) {
            wItem.widgetList.forEach((childItem) => {
              this.buildDataFromWidget(childItem);
            });
          }
        }
      } else if (wItem.formItemFlag) {
        if (!this.formData.hasOwnProperty(wItem.options.name)) {
          // this.formDataModel[wItem.options.name] = '' //这种写法不支持对象属性响应式更新，必须用$set方法！！
          this.$set(
            this.formDataModel,
            wItem.options.name,
            wItem.options.defaultValue,
          ); // 设置字段默认值
        } else {
          let initialValue = this.formData[wItem.options.name];
          this.$set(
            this.formDataModel,
            wItem.options.name,
            deepClone(initialValue),
          );
        }
      }
    },

    addFieldChangeEventHandler() {
      this.$off('fieldChange'); // 移除原有事件监听

      this.$on(
        'fieldChange',
        function (fieldName, newValue, oldValue, subFormName, subFormRowIndex) {
          this.$emit('formDataChange', {
            fieldName: fieldName,
            newValue: newValue,
            oldValue: oldValue,
            formDataModel: this.formDataModel,
            subFormName: subFormName,
            subFormRowIndex: subFormRowIndex,
          });
          this.handleFieldDataChange(
            fieldName,
            newValue,
            oldValue,
            subFormName,
            subFormRowIndex,
          );
          this.$emit(
            'formChange',
            fieldName,
            newValue,
            oldValue,
            this.formDataModel,
            subFormName,
            subFormRowIndex,
          );
        },
      );
    },

    registerFormToRefList() {
      this.widgetRefList['v_form_ref'] = this;
      let vFormRenderList = this.$parent.$children.filter(
        (item) => item.$options.name === 'VFormRender',
      );
      let objData = {};
      vFormRenderList.forEach((item) => {
        // 根据 refName 将其区分开来
        let key = item.formJsonObj.formConfig.refName;
        let refList = this.getComponentRefList(item);
        objData[key] =
          key in objData ? { ...objData[key], ...refList } : refList;
      });
      this.widgetRefList['all_form_ref'] = objData;
    },
    getComponentRefList(componentTree) {
      let refList = null;
      const dfs = (root) => {
        if ('refList' in root) refList = root.refList;

        root.$children &&
          root.$children.forEach((item) => {
            dfs(item);
          });
      };
      dfs(componentTree);
      return refList;
    },
    handleFieldDataChange(
      fieldName,
      newValue,
      oldValue,
      subFormName,
      subFormRowIndex,
    ) {
      if (!!this.formConfig && !!this.formConfig.onFormDataChange) {
        let customFunc = new Function(
          'fieldName',
          'newValue',
          'oldValue',
          'formModel',
          'subFormName',
          'subFormRowIndex',
          this.formConfig.onFormDataChange,
        );
        customFunc.call(
          this,
          fieldName,
          newValue,
          oldValue,
          this.formDataModel,
          subFormName,
          subFormRowIndex,
        );
      }
    },

    handleOnCreated() {
      if (!!this.formConfig && !!this.formConfig.onFormCreated) {
        let customFunc = new Function(this.formConfig.onFormCreated);
        customFunc.call(this);
      }
    },

    handleOnMounted() {
      if (!!this.formConfig && !!this.formConfig.onFormMounted) {
        let customFunc = new Function(this.formConfig.onFormMounted);
        customFunc.call(this);
      }
    },

    handleOnScanCommit(commitCode) {
      if (!!this.formConfig && !!this.formConfig.onScanCommit) {
        let customFunc = new Function(
          'commitCode',
          this.formConfig.onScanCommit,
        );
        customFunc.call(this, commitCode);
      }
    },

    findWidgetAndSetDisabled(widgetName, disabledFlag) {
      let foundW = this.getWidgetRef(widgetName);
      if (foundW) {
        foundW.setDisabled(disabledFlag);
      }
    },

    findWidgetAndSetHidden(widgetName, hiddenFlag) {
      let foundW = this.getWidgetRef(widgetName);
      if (foundW) {
        foundW.setHidden(hiddenFlag);
      }
    },

    // --------------------- 以下为组件支持外部调用的API方法 begin ------------------//
    /* 提示：用户可自行扩充这些方法！！！ */

    changeLanguage(langName) {
      changeLocale(langName);
    },

    getNativeForm() {
      // 获取原生form引用
      return this.$refs['renderForm'];
    },

    getWidgetRef(widgetName, showError = false) {
      let foundRef = this.widgetRefList[widgetName];
      if (!foundRef && !!showError) {
        this.$message.error(this.i18nt('render.hint.refNotFound') + widgetName);
      }
      return foundRef;
    },

    clearFormDataModel() {
      for (let pkey in this.formDataModel) {
        delete this.formDataModel[pkey];
      }
    },

    /**
     * 动态加载表单JSON
     * @param newFormJson
     */
    setFormJson(newFormJson) {
      if (newFormJson) {
        if (
          typeof newFormJson === 'string' ||
          newFormJson.constructor === Object
        ) {
          let newFormJsonObj = null;
          if (typeof newFormJson === 'string') {
            newFormJsonObj = JSON.parse(newFormJson);
          } else {
            newFormJsonObj = newFormJson;
          }

          if (!newFormJsonObj.formConfig || !newFormJsonObj.widgetList) {
            this.$message.error('Invalid format of form json.');
            return;
          }

          /* formDataModel必须在widgetList赋值完成初始化，因为widgetList赋值意味着子组件开始创建！！！ */
          // this.formDataModel = {}  //清空表单数据对象（有bug，会导致表单校验失败！！）
          this.clearFormDataModel(); // 上行代码有问题，会导致表单校验失败，故保留原对象引用只清空对象属性！！
          this.buildFormModel(newFormJsonObj.widgetList);

          this.$set(this.formJsonObj, 'formConfig', newFormJsonObj.formConfig);
          this._provided.formConfig = newFormJsonObj.formConfig; // 强制更新provide的formConfig对象
          this.$set(this.formJsonObj, 'widgetList', newFormJsonObj.widgetList);

          this.initFormObject();
          this.handleOnMounted();
        } else {
          this.$message.error('Set form json failed.');
        }
      }
    },

    /**
     * 重新加载选项数据
     * @param widgetNames 指定重新加载的组件名称或组件名数组，不传则重新加载所有选项字段
     */
    reloadOptionData(widgetNames) {
      let eventParams = [];
      if (!!widgetNames && typeof widgetNames === 'string') {
        eventParams = [widgetNames];
      } else if (!!widgetNames && Array.isArray(widgetNames)) {
        eventParams = [...widgetNames];
      }
      this.broadcast('FieldWidget', 'reloadOptions', [eventParams]);
    },
    getMyFormData(Validation) {
      this.getFormData(Validation);
    },
    getFormData(needValidation = true) {
      if (!needValidation) {
        return this.formDataModel;
      }
      let callback = function nullFunc() {};
      let promise = new window.Promise(function (resolve, reject) {
        callback = function (formData, error) {
          !error ? resolve(formData) : reject(error);
        };
      });

      // // 图片上传接口
      // this.$refs['fieldEditor']

      // 对容器中的字段进行重复性校验
      let validateRepeat = () => {
        // 筛选出所有的key且需要进行做校验的(当前只校验subform(子表单)和trendstab(动态选项卡))
        let keyList = Object.keys(this.formDataModel).filter(
          (item) => item.includes('subform') || item.includes('trendstab'),
        );

        for (let i = 0; i < keyList.length; i++) {
          let item = keyList[i];
          // 大于1才需要进行重复性校验
          if (this.formDataModel[item].length > 1) {
            // 取出所有的需要进行重复校验的key
            let keys = getRepeatWidget(item);
            // 遍历数据
            for (let j = 0; j < keys.length; j++) {
              // 去重后的数组
              let removalArr = [
                ...new Set(
                  this.formDataModel[item].map((item) => item[keys[j]]),
                ),
              ];

              if (removalArr.length === 1 && removalArr[0] !== '') {
                return {
                  data: false,
                  message: '数据项不能出现重复,提示:' + removalArr[0],
                };
              }
            }
          }
          // 必填校验(每一行不能全是空)
          let widget = this.widgetRefList[item]['_props']['widget'].options;
          if (widget.required && !this.formDataModel[item].length) {
            return {
              data: false,
              message:
                widget.validationHint || widget.label + '至少要有一条数据',
            };
            // for (let j = 0; j < this.formDataModel[item].length; j++) {
            //   let rowData = JSON.parse(JSON.stringify(this.formDataModel[item][j]))
            //   // trendstab需要剔除掉title
            //   if (item.includes('trendstab') && 'title' in rowData) {
            //     delete rowData['title']
            //   }
            //   // 去重后的数组
            //   let removalArr = [...new Set(Object.values(rowData))]

            //   if (removalArr.length === 1 && removalArr[0] === '' && Object.keys(rowData).length > 1) {
            //     return {
            //       data: false,
            //       message: widget.validationHint || '子表单或者动态tab不能全为空'
            //     }
            //   }
            // }
          }
        }

        return {
          data: true,
        };
      };

      // 根据容器id找出其中需要做重复性校验的字段
      let getRepeatWidget = (id) => {
        let widgetList = [];
        let dfs = (root) => {
          root.forEach((item) => {
            widgetList.push(item);
            if ('widgetList' in item) {
              dfs(item.widgetList);
            }
          });
        };
        dfs(this.widgetList);
        // 需要校验重复的字段
        return widgetList
          .find((widgetItem) => widgetItem.id === id)
          .widgetList.filter((item) => item.options.validation === 'noRepeat')
          .map((item) => item.id);
      };

      this.$refs['renderForm'].validate((valid) => {
        if (valid) {
          let result = validateRepeat();
          if (!result.data) {
            callback(this.formDataModel, result.message);
            return;
          }
          callback(this.formDataModel);
        } else {
          callback(
            this.formDataModel,
            this.i18nt('render.hint.validationFailed'),
          );
        }
      });

      return promise;
    },

    setFormData(formData) {
      // 设置表单数据
      // this.formDataModel = formData //inject注入的formModel不是响应式的，直接赋值在其他组件拿不到最新值！！

      Object.keys(this.formDataModel).forEach((propName) => {
        if (!!formData && formData.hasOwnProperty(propName)) {
          this.formDataModel[propName] = deepClone(formData[propName]);
        }
      });

      // this.formDataModel = formData
      // this._provided.globalModel.formModel = formData  /* 这种写法可使inject的属性保持响应式更新！！ */
      //

      // 通知SubForm组件：表单数据更新事件！！
      // this.broadcast('ContainerItem', 'setFormData', formData)
      this.broadcast('ContainerItem', 'setFormData', this.formDataModel);

      // 通知FieldWidget组件：表单数据更新事件！！
      // this.broadcast('FieldWidget', 'setFormData', formData)
      this.broadcast('FieldWidget', 'setFormData', this.formDataModel);
    },

    getFieldValue(fieldName) {
      // 单个字段获取值
      let fieldRef = this.getWidgetRef(fieldName);
      if (!!fieldRef && !!fieldRef.getValue) {
        fieldRef.getValue();
      }
    },

    setFieldValue(fieldName, fieldValue) {
      // 单个更新字段值
      let fieldRef = this.getWidgetRef(fieldName);
      if (!!fieldRef && !!fieldRef.setValue) {
        fieldRef.setValue(fieldValue);
      }
    },

    getSubFormValues(subFormName, needValidation = true) {
      let foundSFRef = this.subFormRefList[subFormName];
      // if (!foundSFRef) {
      //   return this.formDataModel[subFormName]
      // }
      return foundSFRef.getSubFormValues(needValidation);
    },

    disableForm() {
      console.log(this.widgetRefList);
      let wNameList = Object.keys(this.widgetRefList);
      wNameList.forEach((wName) => {
        let foundW = this.getWidgetRef(wName);
        if (foundW) {
          // if (!!foundW.setDisabled) {
          //   foundW.setDisabled(true)
          // }

          !!foundW.setDisabled && foundW.setDisabled(true);
        }
      });
    },

    enableForm() {
      let wNameList = Object.keys(this.widgetRefList);
      wNameList.forEach((wName) => {
        let foundW = this.getWidgetRef(wName);
        if (foundW) {
          // if (!!foundW.setDisabled) {
          //   foundW.setDisabled(false)
          // }

          !!foundW.setDisabled && foundW.setDisabled(false);
        }
      });
    },

    resetForm() {
      // 重置表单
      let subFormNames = Object.keys(this.subFormRefList);
      subFormNames.forEach((sfName) => {
        if (this.subFormRefList[sfName].resetSubForm) {
          this.subFormRefList[sfName].resetSubForm();
        }
      });

      let wNameList = Object.keys(this.widgetRefList);
      wNameList.forEach((wName) => {
        let foundW = this.getWidgetRef(wName);
        if (!!foundW && !!foundW.resetField) {
          foundW.resetField();
        }
      });

      this.$nextTick(() => {
        this.clearValidate(); /* 清除resetField方法触发的校验错误提示 */
      });
    },

    clearValidate(props) {
      this.$refs.renderForm.clearValidate(props);
    },

    validateForm() {
      //
    },

    validateFields() {
      //
    },

    disableWidgets(widgetNames) {
      if (widgetNames) {
        if (typeof widgetNames === 'string') {
          this.findWidgetAndSetDisabled(widgetNames, true);
        } else if (Array.isArray(widgetNames)) {
          widgetNames.forEach((wn) => {
            this.findWidgetAndSetDisabled(wn, true);
          });
        }
      }
    },

    enableWidgets(widgetNames) {
      if (widgetNames) {
        if (typeof widgetNames === 'string') {
          this.findWidgetAndSetDisabled(widgetNames, false);
        } else if (Array.isArray(widgetNames)) {
          widgetNames.forEach((wn) => {
            this.findWidgetAndSetDisabled(wn, false);
          });
        }
      }
    },

    hideWidgets(widgetNames) {
      if (widgetNames) {
        if (typeof widgetNames === 'string') {
          this.findWidgetAndSetHidden(widgetNames, true);
        } else if (Array.isArray(widgetNames)) {
          widgetNames.forEach((wn) => {
            this.findWidgetAndSetHidden(wn, true);
          });
        }
      }
    },

    showWidgets(widgetNames) {
      if (widgetNames) {
        if (typeof widgetNames === 'string') {
          this.findWidgetAndSetHidden(widgetNames, false);
        } else if (Array.isArray(widgetNames)) {
          widgetNames.forEach((wn) => {
            this.findWidgetAndSetHidden(wn, false);
          });
        }
      }
    },

    /**
     * 打印渲染出来的Vue2完整内容，可复制后直接使用
     */
    printVueCode() {
      try {
        // 生成完整的Vue2组件代码
        const vueCode = this.generateVueCode();

        // 创建一个新窗口来显示代码
        const newWindow = window.open('', '_blank', 'width=800,height=600');
        newWindow.document.write(`
          <!DOCTYPE html>
          <html>
          <head>
            <title>Vue2 表单代码</title>
            <style>
              body {
                font-family: 'Courier New', monospace;
                margin: 20px;
                background-color: #f5f5f5;
              }
              .code-container {
                background: white;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 20px;
                white-space: pre-wrap;
                overflow: auto;
                max-height: 80vh;
              }
              .copy-btn {
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 10px 20px;
                background: #409EFF;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
              }
              .copy-btn:hover {
                background: #66b1ff;
              }
            </style>
          </head>
          <body>
            <button class="copy-btn" onclick="copyCode()">复制代码</button>
            <div class="code-container" id="codeContent">${this.escapeHtml(vueCode)}</div>
            <script>
              function copyCode() {
                const codeContent = document.getElementById('codeContent').textContent;
                navigator.clipboard.writeText(codeContent).then(() => {
                  alert('代码已复制到剪贴板！');
                }).catch(err => {
                  console.error('复制失败:', err);
                  // 降级方案
                  const textArea = document.createElement('textarea');
                  textArea.value = codeContent;
                  document.body.appendChild(textArea);
                  textArea.select();
                  document.execCommand('copy');
                  document.body.removeChild(textArea);
                  alert('代码已复制到剪贴板！');
                });
              }
            </` + `script>
          </body>
          </html>
        `);
        newWindow.document.close();

        // 同时在控制台输出
        console.log('=== Vue2 表单完整代码 ===');
        console.log(vueCode);
        console.log('=== 代码结束 ===');

        return vueCode;
      } catch (error) {
        console.error('生成Vue代码失败:', error);
        this.$message.error('生成Vue代码失败: ' + error.message);
      }
    },

    /**
     * 生成Vue2组件代码
     */
    generateVueCode() {
      const template = this.generateTemplate();
      const script = this.generateScript();
      const style = this.generateStyle();

      return `<template>
${template}
</template>

<script>
${script}
</ `+ `script>

<style lang="scss" scoped>
${style}
</style>`;
    },

    /**
     * 生成模板部分
     */
    generateTemplate() {
      const formConfig = this.formConfig || {};
      const labelPosition = formConfig.labelPosition || 'left';
      const labelWidth = formConfig.labelWidth ? formConfig.labelWidth + 'px' : '80px';
      const size = formConfig.size || 'medium';
      const customClass = formConfig.customClass || '';

      let template = `  <el-form
    ref="dynamicForm"
    :label-position="${labelPosition}"
    :size="${size}"
    class="dynamic-form ${customClass}"
    :label-width="${labelWidth}"
    :model="formData"
    :rules="formRules"
    @submit.native.prevent
  >`;

      // 生成表单项
      if (this.widgetList && this.widgetList.length > 0) {
        this.widgetList.forEach(widget => {
          template += '\n' + this.generateWidgetTemplate(widget, '    ');
        });
      }

      template += '\n  </el-form>';
      return template;
    },

    /**
     * 生成组件模板
     */
    generateWidgetTemplate(widget, indent = '') {
      if (!widget) return '';

      if (widget.category === 'container') {
        return this.generateContainerTemplate(widget, indent);
      } else {
        return this.generateFieldTemplate(widget, indent);
      }
    },

    /**
     * 生成容器组件模板
     */
    generateContainerTemplate(widget, indent) {
      let template = '';

      switch (widget.type) {
        case 'grid':
          template = `${indent}<el-row :gutter="20">`;
          if (widget.cols && widget.cols.length > 0) {
            widget.cols.forEach(col => {
              const span = col.options?.span || 12;
              template += `\n${indent}  <el-col :span="${span}">`;
              if (col.widgetList && col.widgetList.length > 0) {
                col.widgetList.forEach(childWidget => {
                  template += '\n' + this.generateWidgetTemplate(childWidget, indent + '    ');
                });
              }
              template += `\n${indent}  </el-col>`;
            });
          }
          template += `\n${indent}</el-row>`;
          break;

        case 'tab':
          template = `${indent}<el-tabs v-model="activeTab">`;
          if (widget.tabs && widget.tabs.length > 0) {
            widget.tabs.forEach((tab, index) => {
              template += `\n${indent}  <el-tab-pane label="${tab.options?.label || 'Tab ' + (index + 1)}" name="tab${index}">`;
              if (tab.widgetList && tab.widgetList.length > 0) {
                tab.widgetList.forEach(childWidget => {
                  template += '\n' + this.generateWidgetTemplate(childWidget, indent + '    ');
                });
              }
              template += `\n${indent}  </el-tab-pane>`;
            });
          }
          template += `\n${indent}</el-tabs>`;
          break;

        default:
          // 其他容器类型的处理
          if (widget.widgetList && widget.widgetList.length > 0) {
            widget.widgetList.forEach(childWidget => {
              template += this.generateWidgetTemplate(childWidget, indent);
            });
          }
      }

      return template;
    },

    /**
     * 生成字段组件模板
     */
    generateFieldTemplate(widget, indent) {
      if (!widget.options) return '';

      const options = widget.options;
      const fieldName = options.name;
      const label = options.label || '';
      const required = options.required || false;
      const placeholder = options.placeholder || '';

      let template = `${indent}<el-form-item label="${label}" prop="${fieldName}"${required ? ' required' : ''}>`;

      switch (widget.type) {
        case 'input':
          template += `\n${indent}  <el-input v-model="formData.${fieldName}" placeholder="${placeholder}"></el-input>`;
          break;

        case 'textarea':
          const rows = options.rows || 3;
          template += `\n${indent}  <el-input type="textarea" :rows="${rows}" v-model="formData.${fieldName}" placeholder="${placeholder}"></el-input>`;
          break;

        case 'number':
          template += `\n${indent}  <el-input-number v-model="formData.${fieldName}" placeholder="${placeholder}"></el-input-number>`;
          break;

        case 'radio':
          template += `\n${indent}  <el-radio-group v-model="formData.${fieldName}">`;
          if (options.optionItems && options.optionItems.length > 0) {
            options.optionItems.forEach(item => {
              template += `\n${indent}    <el-radio label="${item.value}">${item.label}</el-radio>`;
            });
          }
          template += `\n${indent}  </el-radio-group>`;
          break;

        case 'checkbox':
          template += `\n${indent}  <el-checkbox-group v-model="formData.${fieldName}">`;
          if (options.optionItems && options.optionItems.length > 0) {
            options.optionItems.forEach(item => {
              template += `\n${indent}    <el-checkbox label="${item.value}">${item.label}</el-checkbox>`;
            });
          }
          template += `\n${indent}  </el-checkbox-group>`;
          break;

        case 'select':
          const multiple = options.multiple || false;
          template += `\n${indent}  <el-select v-model="formData.${fieldName}"${multiple ? ' multiple' : ''} placeholder="${placeholder}">`;
          if (options.optionItems && options.optionItems.length > 0) {
            options.optionItems.forEach(item => {
              template += `\n${indent}    <el-option label="${item.label}" value="${item.value}"></el-option>`;
            });
          }
          template += `\n${indent}  </el-select>`;
          break;

        case 'date':
          template += `\n${indent}  <el-date-picker v-model="formData.${fieldName}" type="date" placeholder="${placeholder}"></el-date-picker>`;
          break;

        case 'time':
          template += `\n${indent}  <el-time-picker v-model="formData.${fieldName}" placeholder="${placeholder}"></el-time-picker>`;
          break;

        case 'switch':
          template += `\n${indent}  <el-switch v-model="formData.${fieldName}"></el-switch>`;
          break;

        case 'slider':
          const min = options.min || 0;
          const max = options.max || 100;
          template += `\n${indent}  <el-slider v-model="formData.${fieldName}" :min="${min}" :max="${max}"></el-slider>`;
          break;

        default:
          template += `\n${indent}  <el-input v-model="formData.${fieldName}" placeholder="${placeholder}"></el-input>`;
      }

      template += `\n${indent}</el-form-item>`;
      return template;
    },

    /**
     * 生成脚本部分
     */
    generateScript() {
      const formData = this.generateFormData();
      const formRules = this.generateFormRules();

      return `export default {
  name: 'DynamicForm',
  data() {
    return {
      activeTab: 'tab0',
      formData: ${JSON.stringify(formData, null, 6)},
      formRules: ${JSON.stringify(formRules, null, 6)}
    }
  },
  methods: {
    // 表单提交
    submitForm() {
      this.$refs.dynamicForm.validate((valid) => {
        if (valid) {
          console.log('表单数据:', this.formData);
          // 在这里处理表单提交逻辑
        } else {
          console.log('表单验证失败');
          return false;
        }
      });
    },

    // 重置表单
    resetForm() {
      this.$refs.dynamicForm.resetFields();
    },

    // 清除验证
    clearValidate() {
      this.$refs.dynamicForm.clearValidate();
    }
  }
}`;
    },

    /**
     * 生成表单数据
     */
    generateFormData() {
      const formData = {};

      const extractFields = (widgetList) => {
        if (!widgetList) return;

        widgetList.forEach(widget => {
          if (widget.category === 'container') {
            if (widget.type === 'grid' && widget.cols) {
              widget.cols.forEach(col => {
                if (col.widgetList) {
                  extractFields(col.widgetList);
                }
              });
            } else if (widget.type === 'tab' && widget.tabs) {
              widget.tabs.forEach(tab => {
                if (tab.widgetList) {
                  extractFields(tab.widgetList);
                }
              });
            } else if (widget.widgetList) {
              extractFields(widget.widgetList);
            }
          } else if (widget.options && widget.options.name) {
            const fieldName = widget.options.name;
            const defaultValue = widget.options.defaultValue;

            // 根据组件类型设置默认值
            switch (widget.type) {
              case 'checkbox':
                formData[fieldName] = defaultValue || [];
                break;
              case 'switch':
                formData[fieldName] = defaultValue || false;
                break;
              case 'number':
              case 'slider':
                formData[fieldName] = defaultValue || 0;
                break;
              default:
                formData[fieldName] = defaultValue || '';
            }
          }
        });
      };

      extractFields(this.widgetList);
      return formData;
    },

    /**
     * 生成表单验证规则
     */
    generateFormRules() {
      const rules = {};

      const extractRules = (widgetList) => {
        if (!widgetList) return;

        widgetList.forEach(widget => {
          if (widget.category === 'container') {
            if (widget.type === 'grid' && widget.cols) {
              widget.cols.forEach(col => {
                if (col.widgetList) {
                  extractRules(col.widgetList);
                }
              });
            } else if (widget.type === 'tab' && widget.tabs) {
              widget.tabs.forEach(tab => {
                if (tab.widgetList) {
                  extractRules(tab.widgetList);
                }
              });
            } else if (widget.widgetList) {
              extractRules(widget.widgetList);
            }
          } else if (widget.options && widget.options.name) {
            const fieldName = widget.options.name;
            const options = widget.options;
            const fieldRules = [];

            // 必填验证
            if (options.required) {
              fieldRules.push({
                required: true,
                message: options.validationHint || `请输入${options.label}`,
                trigger: widget.type === 'select' ? 'change' : 'blur'
              });
            }

            // 长度验证
            if (options.minLength || options.maxLength) {
              const lengthRule = { trigger: 'blur' };
              if (options.minLength) lengthRule.min = options.minLength;
              if (options.maxLength) lengthRule.max = options.maxLength;
              lengthRule.message = `长度在 ${options.minLength || 0} 到 ${options.maxLength || '无限'} 个字符`;
              fieldRules.push(lengthRule);
            }

            // 正则验证
            if (options.regExp) {
              fieldRules.push({
                pattern: new RegExp(options.regExp),
                message: options.validationHint || '格式不正确',
                trigger: 'blur'
              });
            }

            if (fieldRules.length > 0) {
              rules[fieldName] = fieldRules;
            }
          }
        });
      };

      extractRules(this.widgetList);
      return rules;
    },

    /**
     * 生成样式部分
     */
    generateStyle() {
      return `.dynamic-form {
  .el-form-item {
    margin-bottom: 20px;
  }

  .el-select {
    width: 100%;
  }

  .el-date-picker,
  .el-time-picker {
    width: 100%;
  }
}`;
    },

    /**
     * HTML转义
     */
    escapeHtml(text) {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    },

    // --------------------- 以上为组件支持外部调用的API方法 end ------------------//
  },
};
</script>

<style lang="scss" scoped>
.el-form ::v-deep .el-row {
  padding: 8px;
}
::v-deep .el-select {
  width: 100%;
}

</style>
